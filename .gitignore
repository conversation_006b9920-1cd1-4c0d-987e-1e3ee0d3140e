# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
*.pot
*.pyc
db.sqlite3
media/
staticfiles/

# Virtual Environment
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings

# OS
.DS_Store
Thumbs.db
*.bak
*.tmp
*.swp

# Project specific
logs/
*.log
media/avatars/
media/lakes/
media/products/
media/testimonials/

# Sensitive data
.env
*.pem
*.key
