import os
import urllib.request

def download_image(url, filename):
    os.makedirs('static/images', exist_ok=True)
    filepath = os.path.join('static/images', filename)
    urllib.request.urlretrieve(url, filepath)
    print(f'Downloaded {filename}')

# Download store placeholder image (using a different image)
download_image(
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800',
    'store-placeholder.jpg'
)
