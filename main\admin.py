from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Profile, Category, Brand, Product, ProductAttribute, ProductAttributeValue,
    ProductReview, County, Lake, Order, OrderItem, Video, BusinessListing,
    FishingLake, OnlineStore, BusinessImage, Subscription
)

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'created_at']
    search_fields = ['user__username', 'user__email', 'phone']
    list_filter = ['created_at']

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}

@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}

class ProductAttributeValueInline(admin.TabularInline):
    model = ProductAttributeValue
    extra = 1

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'category', 'brand', 'stock', 'is_active']
    list_filter = ['is_active', 'is_featured', 'category', 'brand']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [ProductAttributeValueInline]

@admin.register(ProductAttribute)
class ProductAttributeAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'is_filterable']
    search_fields = ['name', 'description']

@admin.register(ProductReview)
class ProductReviewAdmin(admin.ModelAdmin):
    list_display = ['product', 'user', 'rating', 'created_at']
    list_filter = ['rating', 'created_at']
    search_fields = ['product__name', 'user__username', 'comment']

@admin.register(County)
class CountyAdmin(admin.ModelAdmin):
    list_display = ['name', 'region']
    list_filter = ['region']
    search_fields = ['name', 'region']
    prepopulated_fields = {'slug': ('name',)}

@admin.register(Lake)
class LakeAdmin(admin.ModelAdmin):
    list_display = ['name', 'county', 'is_active']
    list_filter = ['is_active', 'is_featured', 'county']
    search_fields = ['name', 'description']

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['product', 'quantity', 'unit_price']

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'status', 'total', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__username', 'user__email', 'address']
    inlines = [OrderItemInline]
    readonly_fields = ['total']

@admin.register(Video)
class VideoAdmin(admin.ModelAdmin):
    list_display = ['title', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['title', 'description']

class BusinessImageInline(admin.TabularInline):
    model = BusinessImage
    extra = 1

@admin.register(BusinessListing)
class BusinessListingAdmin(admin.ModelAdmin):
    list_display = ['name', 'user', 'listing_type', 'county', 'status', 'created_at']
    list_filter = ['listing_type', 'status', 'county']
    search_fields = ['name', 'description', 'user__username']
    inlines = [BusinessImageInline]
    readonly_fields = ['listing_type']

@admin.register(FishingLake)
class FishingLakeAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'user', 'county', 'status', 'is_eco_friendly', 'created_at'
    ]
    list_filter = [
        'status', 'county', 'catch_release', 'water_quality',
        'eco_waste', 'biodiversity'
    ]
    search_fields = ['name', 'description', 'user__username']
    inlines = [BusinessImageInline]
    readonly_fields = ['listing_type']

    def is_eco_friendly(self, obj):
        is_eco = obj.is_eco_friendly
        return format_html(
            '<span style="color: {};">●</span> {}',
            '#28a745' if is_eco else '#dc3545',
            'Da' if is_eco else 'Nu'
        )
    is_eco_friendly.short_description = 'Eco-friendly'

@admin.register(OnlineStore)
class OnlineStoreAdmin(admin.ModelAdmin):
    list_display = [
        'name', 'user', 'county', 'status', 'is_eco_friendly', 'created_at'
    ]
    list_filter = [
        'status', 'county', 'eco_packaging', 'sustainable_sourcing',
        'carbon_neutral', 'ethical_labor'
    ]
    search_fields = ['name', 'description', 'user__username', 'website_url']
    inlines = [BusinessImageInline]
    readonly_fields = ['listing_type']

    def is_eco_friendly(self, obj):
        is_eco = obj.is_eco_friendly
        return format_html(
            '<span style="color: {};">●</span> {}',
            '#28a745' if is_eco else '#dc3545',
            'Da' if is_eco else 'Nu'
        )
    is_eco_friendly.short_description = 'Eco-friendly'

@admin.register(Subscription)
class SubscriptionAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'plan_name', 'interval', 'status', 'created_at'
    ]
    list_filter = ['plan_name', 'interval', 'status']
    search_fields = ['user__username', 'user__email', 'stripe_customer_id']
    readonly_fields = [
        'stripe_customer_id', 'stripe_subscription_id',
        'start_date', 'end_date'
    ]
