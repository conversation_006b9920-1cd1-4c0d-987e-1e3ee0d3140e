from django.conf import settings

def cart_count(request):
    """Returns the number of items in the user's cart."""
    if not request.user.is_authenticated:
        return {'cart_count': 0}
    
    # TODO: Implement cart count logic
    return {'cart_count': 0}

def stripe_key(request):
    """Returns the Stripe publishable key."""
    return {
        'STRIPE_PUBLISHABLE_KEY': settings.STRIPE_PUBLISHABLE_KEY
    }
