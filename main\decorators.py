from functools import wraps
from django.shortcuts import redirect
from django.contrib import messages

def subscription_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Verifică dacă utilizatorul are un abonament activ
        if not hasattr(request.user, 'subscription') or not request.user.subscription.is_active:
            messages.warning(
                request,
                'Ai nevoie de un abonament activ pentru a accesa această funcționalitate.'
            )
            return redirect('main:subscription_plans')
        
        # Verifică dacă utilizatorul a atins limita de listări pentru planul său
        if hasattr(request.user, 'subscription'):
            current_listings = request.user.businesslisting_set.count()
            if current_listings >= request.user.subscription.max_listings:
                messages.warning(
                    request,
                    'Ai atins limita de listări pentru planul tău curent. '
                    'Actualizează la un plan superior pentru a adăuga mai multe listări.'
                )
                return redirect('main:my_listings')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view
