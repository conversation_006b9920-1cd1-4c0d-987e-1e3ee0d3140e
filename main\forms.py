from django import forms
from .models import FishingLake, OnlineStore, BusinessImage

class BusinessImageFormSet(forms.modelformset_factory(
    BusinessImage,
    fields=('image',),
    extra=3,
    max_num=3,
    validate_max=True,
    can_delete=True
)):
    def clean(self):
        super().clean()
        if any(self.errors):
            return
        
        # Validate at least one image is provided
        valid_forms = [form for form in self.forms if form.cleaned_data and not form.cleaned_data.get('DELETE', False)]
        if not valid_forms:
            raise forms.ValidationError('Trebuie să încarci cel puțin o imagine.')

class FishingLakeForm(forms.ModelForm):
    class Meta:
        model = FishingLake
        fields = [
            'name', 'description', 'county', 'address',
            'latitude', 'longitude',
            'catch_release', 'water_quality', 'eco_waste', 'biodiversity'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 5, 'maxlength': 2000}),
            'latitude': forms.NumberInput(attrs={'step': 'any', 'class': 'form-control'}),
            'longitude': forms.NumberInput(attrs={'step': 'any', 'class': 'form-control'}),
            'catch_release': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'water_quality': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'eco_waste': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'biodiversity': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
        }
        labels = {
            'name': 'Nume Baltă',
            'description': 'Descriere',
            'county': 'Județ',
            'address': 'Adresă',
            'latitude': 'Latitudine',
            'longitude': 'Longitudine',
            'catch_release': 'Catch & Release',
            'water_quality': 'Calitatea Apei',
            'eco_waste': 'Gestionare Deșeuri',
            'biodiversity': 'Biodiversitate',
        }
        help_texts = {
            'description': 'Descrie balta, facilitățile și regulile de pescuit. Maxim 400 de cuvinte.',
            'catch_release': 'Promovezi practica catch & release',
            'water_quality': 'Monitorizezi și menții calitatea apei',
            'eco_waste': 'Gestionezi ecologic deșeurile',
            'biodiversity': 'Protejezi biodiversitatea locală',
        }

    def clean_description(self):
        description = self.cleaned_data.get('description')
        words = len(description.split())
        if words > 400:
            raise forms.ValidationError('Descrierea nu poate depăși 400 de cuvinte.')
        return description

    def clean(self):
        cleaned_data = super().clean()
        latitude = cleaned_data.get('latitude')
        longitude = cleaned_data.get('longitude')
        
        # Validare coordonate în România
        if latitude and longitude:
            if not (43.5 <= float(latitude) <= 48.3):
                self.add_error('latitude', 'Latitudinea trebuie să fie în România (între 43.5 și 48.3)')
            if not (20.2 <= float(longitude) <= 29.7):
                self.add_error('longitude', 'Longitudinea trebuie să fie în România (între 20.2 și 29.7)')
        
        return cleaned_data

class OnlineStoreForm(forms.ModelForm):
    class Meta:
        model = OnlineStore
        fields = [
            'name', 'description', 'county', 'address', 'website_url',
            'eco_packaging', 'sustainable_sourcing', 'carbon_neutral', 'ethical_labor'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 5, 'maxlength': 2000}),
            'website_url': forms.URLInput(attrs={'placeholder': 'https://'}),
            'eco_packaging': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'sustainable_sourcing': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'carbon_neutral': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
            'ethical_labor': forms.CheckboxInput(attrs={'class': 'eco-checkbox'}),
        }
        labels = {
            'name': 'Nume Magazin',
            'description': 'Descriere',
            'county': 'Județ',
            'address': 'Adresă',
            'website_url': 'Website',
            'eco_packaging': 'Ambalaje Eco',
            'sustainable_sourcing': 'Aprovizionare Sustenabilă',
            'carbon_neutral': 'Livrare Carbon Neutră',
            'ethical_labor': 'Practici Etice',
        }
        help_texts = {
            'description': 'Descrie magazinul, produsele și serviciile oferite. Maxim 400 de cuvinte.',
            'website_url': 'URL-ul complet al magazinului (ex: https://www.magazin.ro)',
            'eco_packaging': 'Folosești ambalaje biodegradabile/reciclabile',
            'sustainable_sourcing': 'Produse de la furnizori responsabili',
            'carbon_neutral': 'Compensezi amprenta de carbon',
            'ethical_labor': 'Condiții de muncă echitabile',
        }

    def clean_description(self):
        description = self.cleaned_data.get('description')
        words = len(description.split())
        if words > 400:
            raise forms.ValidationError('Descrierea nu poate depăși 400 de cuvinte.')
        return description

    def clean_website_url(self):
        url = self.cleaned_data.get('website_url')
        if url and not (url.startswith('http://') or url.startswith('https://')):
            url = 'https://' + url
        return url
