from django.core.management.base import BaseCommand
from main.models import County
from django.utils.text import slugify

class Command(BaseCommand):
    help = 'Creates initial counties data'

    def handle(self, *args, **kwargs):
        counties = [
            # Moldova
            {'name': 'Bacău', 'region': 'Moldova'},
            {'name': 'Botoșani', 'region': 'Moldova'},
            {'name': 'Iași', 'region': 'Moldova'},
            {'name': 'Neamț', 'region': 'Moldova'},
            {'name': 'Suceava', 'region': 'Moldova'},
            {'name': 'Vaslui', 'region': 'Moldova'},
            {'name': 'Galați', 'region': 'Moldova'},
            {'name': 'Vrancea', 'region': 'Moldova'},
            
            # Muntenia
            {'name': 'Argeș', 'region': 'Muntenia'},
            {'name': 'Călărași', 'region': 'Muntenia'},
            {'name': 'Dâmbovița', 'region': 'Muntenia'},
            {'name': 'Giurgiu', 'region': 'Muntenia'},
            {'name': '<PERSON><PERSON>mița', 'region': 'Muntenia'},
            {'name': 'Prahova', 'region': 'Muntenia'},
            {'name': 'Teleorman', 'region': 'Muntenia'},
            {'name': 'București', 'region': 'Muntenia'},
            {'name': 'Ilfov', 'region': 'Muntenia'},
            
            # Dobrogea
            {'name': 'Constanța', 'region': 'Dobrogea'},
            {'name': 'Tulcea', 'region': 'Dobrogea'},
            
            # Oltenia
            {'name': 'Dolj', 'region': 'Oltenia'},
            {'name': 'Gorj', 'region': 'Oltenia'},
            {'name': 'Mehedinți', 'region': 'Oltenia'},
            {'name': 'Olt', 'region': 'Oltenia'},
            {'name': 'Vâlcea', 'region': 'Oltenia'},
            
            # Transilvania
            {'name': 'Alba', 'region': 'Transilvania'},
            {'name': 'Brașov', 'region': 'Transilvania'},
            {'name': 'Covasna', 'region': 'Transilvania'},
            {'name': 'Harghita', 'region': 'Transilvania'},
            {'name': 'Mureș', 'region': 'Transilvania'},
            {'name': 'Sibiu', 'region': 'Transilvania'},
            {'name': 'Cluj', 'region': 'Transilvania'},
            {'name': 'Bistrița-Năsăud', 'region': 'Transilvania'},
            {'name': 'Sălaj', 'region': 'Transilvania'},
            {'name': 'Hunedoara', 'region': 'Transilvania'},
            
            # Banat
            {'name': 'Arad', 'region': 'Banat'},
            {'name': 'Caraș-Severin', 'region': 'Banat'},
            {'name': 'Timiș', 'region': 'Banat'},
            
            # Crișana
            {'name': 'Bihor', 'region': 'Crișana'},
            
            # Maramureș
            {'name': 'Maramureș', 'region': 'Maramureș'},
            {'name': 'Satu Mare', 'region': 'Maramureș'},
        ]
        
        for county_data in counties:
            County.objects.get_or_create(
                name=county_data['name'],
                defaults={
                    'slug': slugify(county_data['name']),
                    'region': county_data['region']
                }
            )
            
        self.stdout.write(
            self.style.SUCCESS('Successfully created counties')
        )
