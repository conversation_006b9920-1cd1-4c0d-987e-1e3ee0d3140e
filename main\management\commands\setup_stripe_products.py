from django.core.management.base import BaseCommand
from django.conf import settings
import stripe

stripe.api_key = settings.STRIPE_SECRET_KEY

class Command(BaseCommand):
    help = 'Sets up Stripe products and prices for subscriptions'

    def handle(self, *args, **kwargs):
        # Basic Plan
        basic_product = stripe.Product.create(
            name="Basic",
            description="Perfect pentru începători - 1 listare de afacere"
        )
        
        stripe.Price.create(
            product=basic_product.id,
            unit_amount=4900,  # 49 RON
            currency="ron",
            recurring={"interval": "month"},
            metadata={"plan_id": "price_basic_monthly"}
        )
        
        stripe.Price.create(
            product=basic_product.id,
            unit_amount=49000,  # 490 RON
            currency="ron",
            recurring={"interval": "year"},
            metadata={"plan_id": "price_basic_yearly"}
        )
        
        # Professional Plan
        pro_product = stripe.Product.create(
            name="Professional",
            description="Pentru afaceri în creștere - 5 listări de afaceri"
        )
        
        stripe.Price.create(
            product=pro_product.id,
            unit_amount=9900,  # 99 RON
            currency="ron",
            recurring={"interval": "month"},
            metadata={"plan_id": "price_pro_monthly"}
        )
        
        stripe.Price.create(
            product=pro_product.id,
            unit_amount=99000,  # 990 RON
            currency="ron",
            recurring={"interval": "year"},
            metadata={"plan_id": "price_pro_yearly"}
        )
        
        # Enterprise Plan
        enterprise_product = stripe.Product.create(
            name="Enterprise",
            description="Pentru afaceri mari - Listări nelimitate"
        )
        
        stripe.Price.create(
            product=enterprise_product.id,
            unit_amount=19900,  # 199 RON
            currency="ron",
            recurring={"interval": "month"},
            metadata={"plan_id": "price_enterprise_monthly"}
        )
        
        stripe.Price.create(
            product=enterprise_product.id,
            unit_amount=199000,  # 1990 RON
            currency="ron",
            recurring={"interval": "year"},
            metadata={"plan_id": "price_enterprise_yearly"}
        )
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created Stripe products and prices')
        )
