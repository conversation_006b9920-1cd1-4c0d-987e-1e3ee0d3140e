from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator

class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    phone = models.CharField(max_length=20, blank=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Profil'
        verbose_name_plural = 'Profiluri'

    def __str__(self):
        return f'Profil {self.user.email}'

    def save(self, *args, **kwargs):
        if self.avatar and hasattr(self, '_current_avatar'):
            if self.avatar != self._current_avatar:
                if self._current_avatar:
                    self._current_avatar.storage.delete(self._current_avatar.path)
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        if self.avatar:
            self.avatar.storage.delete(self.avatar.path)
        super().delete(*args, **kwargs)

class Category(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Categorie'
        verbose_name_plural = 'Categorii'

    def __str__(self):
        return self.name

class Brand(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    logo = models.ImageField(upload_to='brands/', blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Brand'
        verbose_name_plural = 'Branduri'

    def __str__(self):
        return self.name

class Product(models.Model):
    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE)
    stock = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Produs'
        verbose_name_plural = 'Produse'

    def __str__(self):
        return self.name

class ProductAttribute(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_filterable = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Atribut Produs'
        verbose_name_plural = 'Atribute Produse'

    def __str__(self):
        return self.name

class ProductAttributeValue(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    attribute = models.ForeignKey(ProductAttribute, on_delete=models.CASCADE)
    value = models.CharField(max_length=100)

    class Meta:
        verbose_name = 'Valoare Atribut'
        verbose_name_plural = 'Valori Atribute'

    def __str__(self):
        return f"{self.product.name} - {self.attribute.name}: {self.value}"

class ProductReview(models.Model):
    product = models.ForeignKey(Product, related_name='reviews', on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Review Produs'
        verbose_name_plural = 'Reviews Produse'
        ordering = ['-created_at']
        unique_together = ['product', 'user']

    def __str__(self):
        return f'Review de la {self.user.email} pentru {self.product.name}'

class County(models.Model):
    name = models.CharField(max_length=100)
    slug = models.SlugField(unique=True)
    region = models.CharField(max_length=100)

    class Meta:
        verbose_name = 'Județ'
        verbose_name_plural = 'Județe'

    def __str__(self):
        return self.name

class Lake(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField()
    county = models.ForeignKey(County, on_delete=models.CASCADE)
    latitude = models.DecimalField(max_digits=9, decimal_places=6)
    longitude = models.DecimalField(max_digits=9, decimal_places=6)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Lac'
        verbose_name_plural = 'Lacuri'

    def __str__(self):
        return self.name

class Order(models.Model):
    STATUS_CHOICES = (
        ('pending', 'În așteptare'),
        ('processing', 'În procesare'),
        ('shipped', 'Expediat'),
        ('delivered', 'Livrat'),
        ('cancelled', 'Anulat')
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total = models.DecimalField(max_digits=10, decimal_places=2)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Comandă'
        verbose_name_plural = 'Comenzi'

    def __str__(self):
        return f"Comanda #{self.id} - {self.user.email}"

class OrderItem(models.Model):
    order = models.ForeignKey(Order, related_name='items', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = 'Produs Comandă'
        verbose_name_plural = 'Produse Comenzi'

    def __str__(self):
        return f"{self.quantity}x {self.product.name}"

class Video(models.Model):
    title = models.CharField(max_length=200)
    description = models.TextField()
    url = models.URLField()
    thumbnail = models.ImageField(upload_to='videos/thumbnails/')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Video'
        verbose_name_plural = 'Videos'

    def __str__(self):
        return self.title

class BusinessListing(models.Model):
    LISTING_TYPES = (
        ('lake', 'Baltă de Pescuit'),
        ('store', 'Magazin Online')
    )
    
    STATUS_CHOICES = (
        ('pending', 'În Așteptare'),
        ('active', 'Activ'),
        ('rejected', 'Respins')
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    listing_type = models.CharField(max_length=10, choices=LISTING_TYPES)
    name = models.CharField(max_length=100)
    description = models.TextField()
    county = models.ForeignKey(County, on_delete=models.PROTECT)
    address = models.CharField(max_length=200)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Listare'
        verbose_name_plural = 'Listări'
    
    def __str__(self):
        return self.name
    
    @property
    def is_eco_friendly(self):
        if hasattr(self, 'fishinglake'):
            return all([
                self.fishinglake.catch_release,
                self.fishinglake.water_quality,
                self.fishinglake.eco_waste,
                self.fishinglake.biodiversity
            ])
        elif hasattr(self, 'onlinestore'):
            return all([
                self.onlinestore.eco_packaging,
                self.onlinestore.sustainable_sourcing,
                self.onlinestore.carbon_neutral,
                self.onlinestore.ethical_labor
            ])
        return False

class FishingLake(BusinessListing):
    latitude = models.DecimalField(max_digits=9, decimal_places=6)
    longitude = models.DecimalField(max_digits=9, decimal_places=6)
    catch_release = models.BooleanField(default=False, verbose_name='Catch & Release Policy')
    water_quality = models.BooleanField(default=False, verbose_name='Water Quality Management')
    eco_waste = models.BooleanField(default=False, verbose_name='Eco-friendly Waste Disposal')
    biodiversity = models.BooleanField(default=False, verbose_name='Local Biodiversity Protection')
    
    def save(self, *args, **kwargs):
        self.listing_type = 'lake'
        super().save(*args, **kwargs)
    
    class Meta:
        verbose_name = 'Baltă de Pescuit'
        verbose_name_plural = 'Bălți de Pescuit'

class OnlineStore(BusinessListing):
    website_url = models.URLField()
    eco_packaging = models.BooleanField(default=False, verbose_name='Ambalaje Eco-friendly')
    sustainable_sourcing = models.BooleanField(default=False, verbose_name='Aprovizionare Sustenabilă')
    carbon_neutral = models.BooleanField(default=False, verbose_name='Livrare Carbon Neutră')
    ethical_labor = models.BooleanField(default=False, verbose_name='Practici Etice de Muncă')
    
    def save(self, *args, **kwargs):
        self.listing_type = 'store'
        super().save(*args, **kwargs)
    
    class Meta:
        verbose_name = 'Magazin Online'
        verbose_name_plural = 'Magazine Online'

class BusinessImage(models.Model):
    listing = models.ForeignKey(BusinessListing, related_name='images', on_delete=models.CASCADE)
    image = models.ImageField(upload_to='business_images/')
    order = models.PositiveSmallIntegerField(default=0)
    
    class Meta:
        ordering = ['order']
        verbose_name = 'Imagine'
        verbose_name_plural = 'Imagini'

class Subscription(models.Model):
    PLAN_CHOICES = (
        ('basic', 'Basic'),
        ('pro', 'Professional'),
        ('enterprise', 'Enterprise')
    )
    
    INTERVAL_CHOICES = (
        ('month', 'Lunar'),
        ('year', 'Anual')
    )
    
    STATUS_CHOICES = (
        ('active', 'Activ'),
        ('past_due', 'Plată Întârziată'),
        ('canceled', 'Anulat'),
        ('incomplete', 'Incomplet')
    )
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    plan_name = models.CharField(max_length=20, choices=PLAN_CHOICES)
    interval = models.CharField(max_length=10, choices=INTERVAL_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='incomplete')
    stripe_customer_id = models.CharField(max_length=50, blank=True)
    stripe_subscription_id = models.CharField(max_length=50, blank=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Abonament'
        verbose_name_plural = 'Abonamente'
    
    def __str__(self):
        return f'{self.user.email} - {self.get_plan_name_display()}'
    
    @property
    def is_active(self):
        return self.status == 'active'
    
    @property
    def max_listings(self):
        if self.plan_name == 'basic':
            return 1
        elif self.plan_name == 'pro':
            return 5
        return float('inf')  # Enterprise plan has unlimited listings
