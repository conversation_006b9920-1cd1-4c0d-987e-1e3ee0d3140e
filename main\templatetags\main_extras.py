from django import template
from datetime import datetime

register = template.Library()

ROMANIAN_DAYS = {
    'Monday': '<PERSON><PERSON>',
    'Tuesday': '<PERSON><PERSON><PERSON>',
    'Wednesday': '<PERSON><PERSON><PERSON><PERSON>',
    'Thursday': '<PERSON><PERSON>',
    'Friday': '<PERSON><PERSON>',
    'Saturday': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    'Sunday': '<PERSON><PERSON><PERSON><PERSON>'
}

ROMANIAN_MONTHS = {
    'January': '<PERSON><PERSON><PERSON>',
    'February': '<PERSON><PERSON><PERSON><PERSON>',
    'March': '<PERSON><PERSON>',
    'April': 'Aprilie',
    'May': 'Mai',
    'June': '<PERSON>unie',
    'July': '<PERSON>ulie',
    'August': 'August',
    'September': 'Septembrie',
    'October': 'Octombrie',
    'November': 'Noie<PERSON>rie',
    'December': 'Decembrie'
}

@register.filter
def romanian_date(value):
    """Convert date to Romanian format"""
    if not value:
        return ''
    
    # Format date in English first
    english_date = value.strftime('%A, %d %B').replace(' 0', ' ')
    
    # Replace day and month names with Romanian equivalents
    for eng, rom in ROMANIAN_DAYS.items():
        english_date = english_date.replace(eng, rom)
    for eng, rom in ROMANIAN_MONTHS.items():
        english_date = english_date.replace(eng, rom)
    
    return english_date

@register.filter
def sub(value, arg):
    """Subtract the arg from the value"""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return value

@register.filter
def multiply(value, arg):
    """Multiply the value by the arg"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return value

@register.filter
def divide(value, arg):
    """Divide the value by the arg"""
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return value
