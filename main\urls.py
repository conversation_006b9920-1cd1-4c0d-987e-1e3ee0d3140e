from django.urls import path
from . import views

app_name = 'main'

urlpatterns = [
    # Business Listings URLs
    path('business/plans/', views.subscription_plans, name='subscription_plans'),
    path('business/create-subscription/', views.create_subscription, name='create_subscription'),
    path('business/webhook/', views.stripe_webhook, name='stripe_webhook'),
    path('business/add-lake/', views.add_fishing_lake, name='add_lake'),
    path('business/add-store/', views.add_online_store, name='add_store'),
    path('business/my-listings/', views.my_listings, name='my_listings'),
    path('business/listings/', views.business_listings, name='business_listings'),
    path('business/listing/<int:pk>/', views.listing_detail, name='listing_detail'),
    
    # Temporary homepage for testing
    path('', views.business_listings, name='home'),  # Using business_listings as homepage for now
    
    # Commented out until implemented
    # path('about/', views.about, name='about'),
    # path('contact/', views.contact, name='contact'),
    # path('terms/', views.terms, name='terms'),
    
    # Account URLs - commented out until implemented
    # path('account/profile/', views.profile, name='profile'),
    # path('account/edit-profile/', views.edit_profile, name='edit_profile'),
    # path('account/change-password/', views.change_password, name='change_password'),
    # path('account/orders/', views.orders, name='orders'),
    # path('account/order/<int:pk>/', views.order_detail, name='order_detail'),
    # path('account/order/<int:pk>/cancel/', views.order_cancel, name='order_cancel'),
    
    # Shop URLs - commented out until implemented
    # path('shop/', views.shop_list, name='shop_list'),
    # path('shop/<slug:slug>/', views.shop_detail, name='shop_detail'),
    # path('cart/', views.cart, name='cart'),
    # path('checkout/', views.checkout, name='checkout'),
    # path('checkout/success/', views.checkout_success, name='checkout_success'),
    # path('checkout/bank-transfer/', views.bank_transfer, name='bank_transfer'),
    
    # Lake URLs - commented out until implemented
    # path('lakes/', views.lake_list, name='lake_list'),
    # path('lakes/map/', views.lake_map, name='lake_map'),
    # path('lakes/<slug:county_slug>/', views.county_lakes, name='county_lakes'),
    # path('lake/<int:pk>/', views.lake_detail, name='lake_detail'),
    
    # Tutorial URLs - commented out until implemented
    # path('tutorials/', views.tutorial_list, name='tutorial_list'),
    # path('tutorial/<int:pk>/', views.tutorial_detail, name='tutorial_detail'),
    
    # Solunar URLs - commented out until implemented
    # path('solunar/', views.solunar_calendar, name='solunar_calendar'),
]
