from django.conf import settings
from django.urls import reverse
import stripe
from ..models import Subscription

stripe.api_key = settings.STRIPE_SECRET_KEY

SUBSCRIPTION_PRICES = {
    'basic': {
        'month': 'price_basic_monthly',  # Înlocuiește cu ID-ul real de la Stripe
        'year': 'price_basic_yearly'
    },
    'pro': {
        'month': 'price_pro_monthly',
        'year': 'price_pro_yearly'
    },
    'enterprise': {
        'month': 'price_enterprise_monthly',
        'year': 'price_enterprise_yearly'
    }
}

def create_checkout_session(request, plan, interval):
    """Creează o sesiune de checkout pentru abonament."""
    if plan not in SUBSCRIPTION_PRICES or interval not in ['month', 'year']:
        raise ValueError('Plan sau interval invalid')
    
    price_id = SUBSCRIPTION_PRICES[plan][interval]
    success_url = request.build_absolute_uri(reverse('main:my_listings'))
    cancel_url = request.build_absolute_uri(reverse('main:subscription_plans'))
    
    # Creează sau actualizează clientul Stripe
    if hasattr(request.user, 'subscription') and request.user.subscription.stripe_customer_id:
        customer_id = request.user.subscription.stripe_customer_id
    else:
        customer = stripe.Customer.create(
            email=request.user.email,
            metadata={
                'user_id': request.user.id
            }
        )
        customer_id = customer.id
    
    # Creează sesiunea de checkout
    session = stripe.checkout.Session.create(
        customer=customer_id,
        payment_method_types=['card'],
        line_items=[{
            'price': price_id,
            'quantity': 1,
        }],
        mode='subscription',
        success_url=success_url + '?session_id={CHECKOUT_SESSION_ID}',
        cancel_url=cancel_url,
        metadata={
            'user_id': request.user.id,
            'plan': plan,
            'interval': interval
        }
    )
    
    return session

def handle_checkout_completed(event):
    """Procesează evenimentul de finalizare checkout."""
    session = event.data.object
    user_id = int(session.metadata.get('user_id'))
    plan = session.metadata.get('plan')
    interval = session.metadata.get('interval')
    
    subscription = Subscription.objects.filter(user_id=user_id).first()
    if subscription:
        subscription.stripe_customer_id = session.customer
        subscription.stripe_subscription_id = session.subscription
        subscription.plan_name = plan
        subscription.interval = interval
        subscription.status = 'active'
        subscription.save()
    else:
        Subscription.objects.create(
            user_id=user_id,
            stripe_customer_id=session.customer,
            stripe_subscription_id=session.subscription,
            plan_name=plan,
            interval=interval,
            status='active'
        )

def handle_subscription_updated(event):
    """Procesează evenimentul de actualizare abonament."""
    subscription = event.data.object
    stripe_customer_id = subscription.customer
    
    db_subscription = Subscription.objects.filter(
        stripe_customer_id=stripe_customer_id
    ).first()
    
    if db_subscription:
        db_subscription.status = subscription.status
        db_subscription.save()

def handle_subscription_deleted(event):
    """Procesează evenimentul de ștergere abonament."""
    subscription = event.data.object
    stripe_customer_id = subscription.customer
    
    Subscription.objects.filter(
        stripe_customer_id=stripe_customer_id
    ).update(status='canceled')
