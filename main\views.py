from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import transaction
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.conf import settings
from .models import (
    Product, Category, Order, OrderItem, Lake, County,
    BusinessListing, FishingLake, OnlineStore, BusinessImage,
    Subscription
)
from .forms import FishingLakeForm, OnlineStoreForm, BusinessImageFormSet
from .decorators import subscription_required
from .utils.subscription import (
    create_checkout_session, handle_checkout_completed,
    handle_subscription_updated, handle_subscription_deleted
)
import stripe
import json

stripe.api_key = settings.STRIPE_SECRET_KEY

def subscription_plans(request):
    """Afișează planurile de abonament disponibile."""
    return render(request, 'business/subscription_plans.html')

@login_required
def create_subscription(request):
    """Creează o sesiune de checkout Stripe pentru abonament."""
    if request.method != 'POST':
        return redirect('main:subscription_plans')
    
    plan = request.POST.get('plan')
    interval = request.POST.get('interval')
    
    try:
        session = create_checkout_session(request, plan, interval)
        return JsonResponse({'sessionId': session.id})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)

@csrf_exempt
def stripe_webhook(request):
    """Webhook pentru procesarea evenimentelor Stripe."""
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError:
        return JsonResponse({'error': 'Invalid payload'}, status=400)
    except stripe.error.SignatureVerificationError:
        return JsonResponse({'error': 'Invalid signature'}, status=400)
    
    if event['type'] == 'checkout.session.completed':
        handle_checkout_completed(event)
    elif event['type'] == 'customer.subscription.updated':
        handle_subscription_updated(event)
    elif event['type'] == 'customer.subscription.deleted':
        handle_subscription_deleted(event)
    
    return JsonResponse({'status': 'success'})

@login_required
@subscription_required
def add_fishing_lake(request):
    """Adaugă o nouă baltă de pescuit."""
    if request.method == 'POST':
        form = FishingLakeForm(request.POST)
        formset = BusinessImageFormSet(
            request.POST, request.FILES,
            queryset=BusinessImage.objects.none()
        )
        
        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    lake = form.save(commit=False)
                    lake.user = request.user
                    lake.save()
                    
                    images = formset.save(commit=False)
                    for image in images:
                        image.listing = lake
                        image.save()
                    
                    messages.success(
                        request,
                        'Balta a fost adăugată cu succes! Va fi revizuită în curând.'
                    )
                    return redirect('main:my_listings')
            except Exception as e:
                messages.error(
                    request,
                    'A apărut o eroare la salvarea bălții. Te rugăm să încerci din nou.'
                )
    else:
        form = FishingLakeForm()
        formset = BusinessImageFormSet(queryset=BusinessImage.objects.none())
    
    return render(request, 'business/add_lake.html', {
        'form': form,
        'formset': formset
    })

@login_required
@subscription_required
def add_online_store(request):
    """Adaugă un nou magazin online."""
    if request.method == 'POST':
        form = OnlineStoreForm(request.POST)
        formset = BusinessImageFormSet(
            request.POST, request.FILES,
            queryset=BusinessImage.objects.none()
        )
        
        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    store = form.save(commit=False)
                    store.user = request.user
                    store.save()
                    
                    images = formset.save(commit=False)
                    for image in images:
                        image.listing = store
                        image.save()
                    
                    messages.success(
                        request,
                        'Magazinul a fost adăugat cu succes! Va fi revizuit în curând.'
                    )
                    return redirect('main:my_listings')
            except Exception as e:
                messages.error(
                    request,
                    'A apărut o eroare la salvarea magazinului. Te rugăm să încerci din nou.'
                )
    else:
        form = OnlineStoreForm()
        formset = BusinessImageFormSet(queryset=BusinessImage.objects.none())
    
    return render(request, 'business/add_store.html', {
        'form': form,
        'formset': formset
    })

@login_required
def my_listings(request):
    """Afișează listările utilizatorului curent."""
    listings = BusinessListing.objects.filter(user=request.user)
    return render(request, 'business/my_listings.html', {
        'listings': listings
    })

def business_listings(request):
    """Afișează toate listările active."""
    lakes = FishingLake.objects.filter(status='active')
    stores = OnlineStore.objects.filter(status='active')
    
    return render(request, 'business/listings.html', {
        'lakes': lakes,
        'stores': stores
    })

def listing_detail(request, pk):
    """Afișează detaliile unei listări."""
    listing = get_object_or_404(BusinessListing, pk=pk, status='active')
    return render(request, 'business/listing_detail.html', {
        'listing': listing
    })
