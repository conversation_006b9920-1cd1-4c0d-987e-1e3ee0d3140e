/* Filter Sidebar */
@media (max-width: 991.98px) {
    #filterSidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: white;
        z-index: 1050;
        overflow-y: auto;
        transition: left 0.3s ease-in-out;
        padding: 1rem;
    }

    #filterSidebar.show {
        left: 0;
    }

    body.filter-open {
        overflow: hidden;
    }

    #filterForm {
        padding-bottom: 4rem;
    }
}

/* Price Range Slider */
.noUi-connect {
    background: #198754;
}

.noUi-horizontal {
    height: 8px;
}

.noUi-handle {
    border: 1px solid #198754;
    background: #fff;
    border-radius: 50%;
    cursor: pointer;
}

.noUi-handle:before,
.noUi-handle:after {
    display: none;
}

/* Product Cards */
.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}


/* Loading State */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
}

/* Filter Toggle Button */
#filterToggle {
    width: 50px;
    height: 50px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#filterToggle i {
    font-size: 1.2rem;
}

/* Attribute Filters */
.color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
    border: 1px solid #dee2e6;
}

/* Rating Stars */
.rating-stars {
    color: #ffc107;
}

.rating-stars.empty {
    color: #dee2e6;
}

/* Sort Select */
#sortSelect {
    min-width: 200px;
}

/* Results Count */
#resultsCount {
    min-width: 100px;
    text-align: right;
}

/* Pagination */
.pagination .page-link {
    color: #198754;
}

.pagination .page-item.active .page-link {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

/* Toast Notification */
.toast {
    background: white;
    border-left: 4px solid #198754;
}

.toast-header {
    background: rgba(25, 135, 84, 0.1);
    color: #198754;
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}
