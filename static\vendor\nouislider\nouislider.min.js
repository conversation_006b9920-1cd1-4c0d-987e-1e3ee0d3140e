/*! nouislider - 14.6.3 - 11/19/2020 */
!function(t){"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?module.exports=t():window.noUiSlider=t()}(function(){"use strict";var lt="14.6.3";function ct(t){t.parentElement.removeChild(t)}function pt(t){return null!=t}function ut(t){t.preventDefault()}function ft(t){return"number"==typeof t&&!isNaN(t)&&isFinite(t)}function dt(t,e,r){r>0&&(gt(t,e),setTimeout(function(){mt(t,e)},r))}function ht(t){return Math.max(Math.min(t,100),0)}function mt(t,e){t.classList&&!/\s/.test(e)?t.classList.add(e):t.className+=" "+e}function gt(t,e){t.classList&&!/\s/.test(e)?t.classList.remove(e):t.className=t.className.replace(new RegExp("(^|\\b)"+e.split(" ").join("|")+"(\\b|$)","gi")," ")}function vt(t){var e=void 0!==window.pageXOffset,r="CSS1Compat"===(t.compatMode||"");return{x:e?window.pageXOffset:r?t.documentElement.scrollLeft:t.body.scrollLeft,y:e?window.pageYOffset:r?t.documentElement.scrollTop:t.body.scrollTop}}function bt(t,e){return 100/(e-t)}function xt(t,e,r){return 100*e/(t[r+1]-t[r])}function St(t,e){for(var r=1;t>=e[r];)r+=1;return r}function t(t,e,r){if(r>=t.slice(-1)[0])return 100;var n,i,o=St(r,t),s=t[o-1],a=t[o],l=e[o-1],c=e[o];return l+(i=r,xt(n=[s,a],n[0]<0?i+Math.abs(n[0]):i-n[0],0)/bt(l,c))}function e(t,e,r,n){if(100===n)return n;var i,o,s=St(n,t),a=t[s-1],l=t[s];return r?(l-a)/2<n-a?l:a:e[s-1]?t[s-1]+(i=n-t[s-1],o=e[s-1],Math.round(i/o)*o):n}function wt(t,e,r){var n;if("number"==typeof e&&(e=[e]),!Array.isArray(e))throw new Error("noUiSlider ("+lt+"): 'range' contains invalid value.");if(!ft(n="min"===t?0:"max"===t?100:parseFloat(t))||!ft(e[0]))throw new Error("noUiSlider ("+lt+"): 'range' value isn't numeric.");r.xPct.push(n),r.xVal.push(e[0]),n?r.xSteps.push(!isNaN(e[1])&&e[1]):isNaN(e[1])||(r.xSteps[0]=e[1]),r.xHighestCompleteStep.push(0)}function yt(t,e,r){if(e)if(r.xVal[t]!==r.xVal[t+1]){r.xSteps[t]=xt([r.xVal[t],r.xVal[t+1]],e,0)/bt(r.xPct[t],r.xPct[t+1]);var n=(r.xVal[t+1]-r.xVal[t])/r.xNumSteps[t],i=Math.ceil(Number(n.toFixed(3))-1),o=r.xVal[t]+r.xNumSteps[t]*i;r.xHighestCompleteStep[t]=o}else r.xSteps[t]=r.xHighestCompleteStep[t]=r.xVal[t]}function r(t,e,r){var n;this.xPct=[],this.xVal=[],this.xSteps=[r||!1],this.xNumSteps=[!1],this.xHighestCompleteStep=[],this.snap=e;var i=[];for(n in t)t.hasOwnProperty(n)&&i.push([t[n],n]);for(i.length&&"object"==typeof i[0][0]?i.sort(function(t,e){return t[0][0]-e[0][0]}):i.sort(function(t,e){return t[0]-e[0]}),n=0;n<i.length;n++)wt(i[n][1],i[n][0],this);for(this.xNumSteps=this.xSteps.slice(0),n=0;n<this.xNumSteps.length;n++)yt(n,this.xNumSteps[n],this)}r.prototype.getDistance=function(t){var e,r=[];for(e=0;e<this.xNumSteps.length-1;e++){var n=this.xNumSteps[e];if(n&&t/n%1!=0)throw new Error("noUiSlider ("+lt+"): 'limit', 'margin' and 'padding' of "+this.xPct[e]+"% range must be divisible by step.");r[e]=xt(this.xVal,t,e)}return r},r.prototype.getAbsoluteDistance=function(t,e,r){var n,i=0;if(t<this.xPct[this.xPct.length-1])for(;t>this.xPct[i+1];)i++;else t===this.xPct[this.xPct.length-1]&&(i=this.xPct.length-2);r||t!==this.xPct[i+1]||i++;var o=1,s=e[i],a=0,l=0,c=0,p=0;for(n=r?(t-this.xPct[i])/(this.xPct[i+1]-this.xPct[i]):(this.xPct[i+1]-t)/(this.xPct[i+1]-this.xPct[i]);0<s;)a=this.xPct[i+1+p]-this.xPct[i+p],100<e[i+p]*o+100-100*n?(l=a*n,o=(s-100*n)/e[i+p],n=1):(l=e[i+p]*a/100*o,o=0),r?(c-=l,1<=this.xPct.length+p&&p--):(c+=l,1<=this.xPct.length-p&&p++),s=e[i+p]*o;return t+c},r.prototype.toStepping=function(t){return t=t(this.xVal,this.xPct)},r.prototype.fromStepping=function(t){return function(t,e,r){if(100<=r)return t.slice(-1)[0];var n,i=St(r,e),o=t[i-1],s=t[i],a=e[i-1],l=e[i];return n=[o,s],(r-a)*bt(a,l)*(n[1]-n[0])/100+n[0]}(this.xVal,this.xPct,t)},r.prototype.getStep=function(t){return t=e(this.xPct,this.xSteps,this.snap,t)},r.prototype.getDefaultStep=function(t,e,r){var n=St(t,this.xPct);return(100===t||e&&t===this.xPct[n-1])&&(n=Math.max(n-1,1)),(this.xVal[n]-this.xVal[n-1])/r},r.prototype.getNearbySteps=function(t){var e=St(t,this.xPct);return{stepBefore:{startValue:this.xVal[e-2],step:this.xNumSteps[e-2],highestStep:this.xHighestCompleteStep[e-2]},thisStep:{startValue:this.xVal[e-1],step:this.xNumSteps[e-1],highestStep:this.xHighestCompleteStep[e-1]},stepAfter:{startValue:this.xVal[e],step:this.xNumSteps[e],highestStep:this.xHighestCompleteStep[e]}}},r.prototype.countStepDecimals=function(){var t=this.xNumSteps.map(n);return Math.max.apply(null,t)},r.prototype.convert=function(t){return this.getStep(this.toStepping(t))};var Et={to:function(t){return void 0!==t&&t.toFixed(2)},from:Number},Ct={target:"target",base:"base",origin:"origin",handle:"handle",handleLower:"handle-lower",handleUpper:"handle-upper",touchArea:"touch-area",horizontal:"horizontal",vertical:"vertical",background:"background",connect:"connect",connects:"connects",ltr:"ltr",rtl:"rtl",textDirectionLtr:"txt-dir-ltr",textDirectionRtl:"txt-dir-rtl",draggable:"draggable",drag:"state-drag",tap:"state-tap",active:"active",tooltip:"tooltip",pips:"pips",pipsHorizontal:"pips-horizontal",pipsVertical:"pips-vertical",marker:"marker",markerHorizontal:"marker-horizontal",markerVertical:"marker-vertical",markerNormal:"marker-normal",markerLarge:"marker-large",markerSub:"marker-sub",value:"value",valueHorizontal:"value-horizontal",valueVertical:"value-vertical",valueNormal:"value-normal",valueLarge:"value-large",valueSub:"value-sub"};function n(t){if("number"==typeof t)return t;var e=t.split("."),r=e[0],n=e[1];return 1===e.length?parseFloat(r):(r+"."+n).length-1}function Nt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'step' is not numeric.");t.singleStep=e}function Pt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'keyboardPageMultiplier' is not numeric.");t.keyboardPageMultiplier=e}function kt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'keyboardDefaultStep' is not numeric.");t.keyboardDefaultStep=e}function Ut(t,e){if("object"!=typeof e||Array.isArray(e))throw new Error("noUiSlider ("+lt+"): 'range' is not an object.");if(void 0===e.min||void 0===e.max)throw new Error("noUiSlider ("+lt+"): Missing 'min' or 'max' in 'range'.");if(e.min===e.max)throw new Error("noUiSlider ("+lt+"): 'range' 'min' and 'max' cannot be equal.");t.spectrum=new r(e,t.snap,t.singleStep)}function At(t,e){if(e=Tt(e),!Array.isArray(e)||!e.length)throw new Error("noUiSlider ("+lt+"): 'start' option is incorrect.");t.handles=e.length,t.start=e}function Vt(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'snap' option must be a boolean.");t.snap=e}function Dt(t,e){if("boolean"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'animate' option must be a boolean.");t.animate=e}function Mt(t,e){if("number"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'animationDuration' option must be a number.");t.animationDuration=e}function Ot(t,e){var r,n=[!1];if("lower"===e?e=[!0,!1]:"upper"===e&&(e=[!1,!0]),!0===e||!1===e){for(r=1;r<t.handles;r++)n.push(e);n.push(!1)}else{if(!Array.isArray(e)||!e.length||e.length!==t.handles+1)throw new Error("noUiSlider ("+lt+"): 'connect' option doesn't match handle count.");n=e}t.connect=n}function Lt(t,e){switch(e){case"horizontal":t.ort=0;break;case"vertical":t.ort=1;break;default:throw new Error("noUiSlider ("+lt+"): 'orientation' option is invalid.")}}function jt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'margin' option must be numeric.");0!==e&&(t.margin=t.spectrum.getDistance(e))}function zt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'limit' option must be numeric.");if(t.limit=t.spectrum.getDistance(e),!t.limit||t.handles<2)throw new Error("noUiSlider ("+lt+"): 'limit' option is only supported on linear sliders with 2 or more handles.")}function Ht(t,e){var r;if(!ft(e)&&!Array.isArray(e))throw new Error("noUiSlider ("+lt+"): 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&2!==e.length&&!ft(e[0])&&!ft(e[1]))throw new Error("noUiSlider ("+lt+"): 'padding' option must be numeric or array of exactly 2 numbers.");if(0!==e){for(Array.isArray(e)||(e=[e,e]),t.padding=[t.spectrum.getDistance(e[0]),t.spectrum.getDistance(e[1])],r=0;r<t.spectrum.xNumSteps.length-1;r++)if(t.padding[0][r]<0||t.padding[1][r]<0)throw new Error("noUiSlider ("+lt+"): 'padding' option must be a positive number(s).");var n=e[0]+e[1],i=t.spectrum.xVal[0];if(1<n/(t.spectrum.xVal[t.spectrum.xVal.length-1]-i))throw new Error("noUiSlider ("+lt+"): 'padding' option must not exceed 100% of the range.")}}function Ft(t,e){switch(e){case"ltr":t.dir=0;break;case"rtl":t.dir=1;break;default:throw new Error("noUiSlider ("+lt+"): 'direction' option was not recognized.")}}function Rt(t,e){if("string"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'behaviour' must be a string containing options.");var r=0<=e.indexOf("tap"),n=0<=e.indexOf("drag"),i=0<=e.indexOf("fixed"),o=0<=e.indexOf("snap"),s=0<=e.indexOf("hover"),a=0<=e.indexOf("unconstrained");if(i){if(2!==t.handles)throw new Error("noUiSlider ("+lt+"): 'fixed' behaviour must be used with 2 handles");jt(t,t.start[1]-t.start[0])}if(a&&(t.margin||t.limit))throw new Error("noUiSlider ("+lt+"): 'unconstrained' behaviour cannot be used with margin or limit");t.events={tap:r||o,drag:n,fixed:i,snap:o,hover:s,unconstrained:a}}function Tt(t){if(!Array.isArray(t)&&!1!==t)throw new Error("noUiSlider ("+lt+"): 'connect' option must be true, false or an array.");if(Array.isArray(t)&&1<t.length&&!t.every(function(t){return"boolean"==typeof t}))throw new Error("noUiSlider ("+lt+"): 'connect' option must have all elements of type boolean.")}function Bt(t,e){switch(e){case"horizontal":t.ort=0;break;case"vertical":t.ort=1;break;default:throw new Error("noUiSlider ("+lt+"): 'orientation' option is invalid.")}}function qt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'margin' option must be numeric.");0!==e&&(t.margin=t.spectrum.getDistance(e))}function Xt(t,e){if(!ft(e))throw new Error("noUiSlider ("+lt+"): 'limit' option must be numeric.");if(t.limit=t.spectrum.getDistance(e),!t.limit||t.handles<2)throw new Error("noUiSlider ("+lt+"): 'limit' option is only supported on linear sliders with 2 or more handles.")}function Yt(t,e){var r;if(!ft(e)&&!Array.isArray(e))throw new Error("noUiSlider ("+lt+"): 'padding' option must be numeric or array of exactly 2 numbers.");if(Array.isArray(e)&&2!==e.length&&!ft(e[0])&&!ft(e[1]))throw new Error("noUiSlider ("+lt+"): 'padding' option must be numeric or array of exactly 2 numbers.");if(0!==e){for(Array.isArray(e)||(e=[e,e]),t.padding=[t.spectrum.getDistance(e[0]),t.spectrum.getDistance(e[1])],r=0;r<t.spectrum.xNumSteps.length-1;r++)if(t.padding[0][r]<0||t.padding[1][r]<0)throw new Error("noUiSlider ("+lt+"): 'padding' option must be a positive number(s).");var n=e[0]+e[1],i=t.spectrum.xVal[0];if(1<n/(t.spectrum.xVal[t.spectrum.xVal.length-1]-i))throw new Error("noUiSlider ("+lt+"): 'padding' option must not exceed 100% of the range.")}}function Wt(t,e){switch(e){case"ltr":t.dir=0;break;case"rtl":t.dir=1;break;default:throw new Error("noUiSlider ("+lt+"): 'direction' option was not recognized.")}}function Gt(t,e){if("string"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'cssPrefix' must be a string.");t.cssPrefix=e}function Jt(t,e){if("object"!=typeof e)throw new Error("noUiSlider ("+lt+"): 'cssClasses' must be an object.");if("string"==typeof t.cssPrefix)for(var r in t.cssClasses={},e)e.hasOwnProperty(r)&&(t.cssClasses[r]=t.cssPrefix+e[r]);else t.cssClasses=e}function i(e){var r={margin:0,limit:0,padding:0,animate:!0,animationDuration:300,ariaFormat:Et,format:Et},n={step:{r:!1,t:Nt},keyboardPageMultiplier:{r:!1,t:Pt},keyboardDefaultStep:{r:!1,t:kt},start:{r:!0,t:At},connect:{r:!0,t:Ot},direction:{r:!0,t:Ft},snap:{r:!1,t:Vt},animate:{r:!1,t:Dt},animationDuration:{r:!1,t:Mt},range:{r:!0,t:Ut},orientation:{r:!1,t:Lt},margin:{r:!1,t:jt},limit:{r:!1,t:zt},padding:{r:!1,t:Ht},behaviour:{r:!0,t:Rt},ariaFormat:{r:!1,t:Q},format:{r:!1,t:K},tooltips:{r:!1,t:It},keyboardSupport:{r:!0,t:$t},documentElement:{r:!1,t:_t},cssPrefix:{r:!0,t:Gt},cssClasses:{r:!0,t:Jt}},i={connect:!1,direction:"ltr",behaviour:"tap",orientation:"horizontal",keyboardSupport:!0,cssPrefix:"noUi-",cssClasses:Ct,keyboardPageMultiplier:5,keyboardDefaultStep:10};e.format&&!e.ariaFormat&&(e.ariaFormat=e.format),Object.keys(n).forEach(function(t){if(!pt(e[t])&&void 0===i[t]){if(n[t].r)throw new Error("noUiSlider ("+lt+"): '"+t+"' is required.");return!0}n[t].t(r,pt(e[t])?e[t]:i[t])}),r.pips=e.pips;var t=document.createElement("div"),o=void 0!==t.style.msTransform,s=void 0!==t.style.transform;r.transformRule=s?"transform":o?"msTransform":"webkitTransform";return r.style=[["left","top"],["right","bottom"]][r.dir][r.ort],r}function Kt(t,f,o){var l,u,s,c,i,a,e,p,d=window.navigator.pointerEnabled?{start:"pointerdown",move:"pointermove",end:"pointerup"}:window.navigator.msPointerEnabled?{start:"MSPointerDown",move:"MSPointerMove",end:"MSPointerUp"}:{start:"mousedown touchstart",move:"mousemove touchmove",end:"mouseup touchend"},h=window.CSS&&CSS.supports&&CSS.supports("touch-action","none")&&function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,e)}catch(t){}return t}(),y=t,E=f.spectrum,x=[],S=[],m=[],g=0,v={},b=t.ownerDocument,w=f.documentElement||b.documentElement,C=b.body,N=-1,P=0,k=1,U=2,A="rtl"===b.dir||1===f.ort?0:100;function V(t,e){var r=b.createElement("div");return e&&mt(r,e),t.appendChild(r),r}function D(t,e){var r=V(t,f.cssClasses.origin),n=V(r,f.cssClasses.handle);return V(n,f.cssClasses.touchArea),n.setAttribute("data-handle",e),f.keyboardSupport&&(n.setAttribute("tabindex","0"),n.addEventListener("keydown",function(t){return function(t,e){if(L()||j(e))return!1;var r=["Left","Right"],n=["Down","Up"],i=["PageDown","PageUp"],o=["Home","End"];f.dir&&!f.ort?r.reverse():f.ort&&!f.dir&&(n.reverse(),i.reverse());var s,a=t.key.replace("Arrow",""),l=a===i[0],c=a===i[1],p=a===n[0]||a===r[0]||l,u=a===n[1]||a===r[1]||c,d=a===o[0],h=a===o[1];if(!(p||u||d||h))return!0;if(t.preventDefault(),u||p){var m=p?0:1,g=at(e),v=g[m];if(null===v)return!1;!1===v&&(v=E.getDefaultStep(S[e],p,f.keyboardDefaultStep)),v*=c||l?f.keyboardPageMultiplier:f.keyboardDefaultStep,v=Math.max(v,1e-7),v*=p?-1:1,s=x[e]+v}else s=h?f.spectrum.xVal[f.spectrum.xVal.length-1]:f.spectrum.xVal[0];return rt(e,E.toStepping(s),!0,!0),K("slide",e),K("update",e),K("change",e),K("set",e),!1}(t,e)})),n.setAttribute("role","slider"),n.setAttribute("aria-orientation",f.ort?"vertical":"horizontal"),0===e?mt(n,f.cssClasses.handleLower):e===f.handles-1&&mt(n,f.cssClasses.handleUpper),r}function M(t,e){return!!e&&V(t,f.cssClasses.connect)}function r(t,e){return!!f.tooltips[e]&&V(t.firstChild,f.cssClasses.tooltip)}function O(t,e,r){var n=b.createElement("div"),i=[];i[P]=f.cssClasses.valueNormal,i[k]=f.cssClasses.valueLarge,i[U]=f.cssClasses.valueSub;var o=[];o[P]=f.cssClasses.markerNormal,o[k]=f.cssClasses.markerLarge,o[U]=f.cssClasses.markerSub;var s=[f.cssClasses.valueHorizontal,f.cssClasses.valueVertical],a=[f.cssClasses.markerHorizontal,f.cssClasses.markerVertical];function l(t,e){var r=e===f.cssClasses.value,n=r?i:o;return e+" "+(r?s:a)[f.ort]+" "+n[t]}return mt(n,f.cssClasses.pips),mt(n,0===f.ort?f.cssClasses.pipsHorizontal:f.cssClasses.pipsVertical),Object.keys(t).forEach(function(i){!function(t,e,r){if((r=e?e(t):t)!==N){var n=V(n,l(r,f.cssClasses.marker)),i=n.appendChild(b.createElement("div"));i.style[f.style]=t+"%","horizontal"===f.orientation&&(i.style.width="100%"),i.appendChild(b.createTextNode(String(r)))}}(i,e,r)}),n}function L(){return u&&(ct(u),u=null),u}function j(t){return!!t.getAttribute("disabled")}function z(t,e){t.stopPropagation();var r,n,i,o=e.getAttribute("data-handle"),s=x[o],a=S[o],l=m[o],c=l.map(function(t){return t.startCalcPoint}),p=l.map(function(t){return t.endCalcPoint}),u=e.getBoundingClientRect(),d=e.getAttribute("data-range-points").split(","),h=d.map(function(t){return parseFloat(t)}),f=h[0],g=h[1],v=u.left,b=u.top,w=u.width,C=u.height,N=f+w*(g-f)/100;r=0===o?s:a,n=E.toStepping(r),i=E.toStepping(N),rt(o,i-n,!0,!1),K("slide",o)}function H(t,e){var r=V(e,f.cssClasses.connects);u=[];for(var n=0;n<f.handles;n++){var i=D(e,n);u.push(i)}a=r,Object.keys(t).forEach(function(t){!function(t,e,r){if(!1!==e)if(1===f.handles){var n=u[0];n.style[t]=e+"%",n.style[r?"top":"left"]=0}else{var i=u[0],o=u[1],s=parseFloat(e);i.style[t]=s+"%",o.style[t]=100-s+"%",i.style[r?"top":"left"]=0,o.style[r?"top":"left"]=0}}(t,e[t],f.ort),mt(u[t],f.cssClasses.draggable),u[t].addEventListener("mousedown",z),u[t].addEventListener("touchstart",z)})}function F(t,e,r){var n=t.mode,i=t.density||1,o=t.filter||!1,s=function(t,e,r){if("range"===t||"steps"===t)return E.xVal;if("count"===t){if(e<2)throw new Error("noUiSlider ("+lt+"): 'values' (>= 2) required for mode 'count'.");var n=e-1,i=100/n;for(e=[];n--;)e[n]=n*i;e.push(100),t="positions"}return"positions"===t?e.map(function(t){return E.fromStepping(r?E.getStep(t):t)}):"values"===t?r?e.map(function(t){return E.fromStepping(E.getStep(E.toStepping(t)))}):e:void 0}(n,t.values||!1,t.stepped||!1),a=function(t,e,r){var n,i={},o=E.xVal[0],s=E.xVal[E.xVal.length-1],a=!1,l=!1,c=0;return(n=r.slice().sort(function(t,e){return t-e}),r=n.filter(function(t){return!this[t]&&(this[t]=!0)},{}))[0]!==o&&(r.unshift(o),a=!0),r[r.length-1]!==s&&(r.push(s),l=!0),r.forEach(function(n,o){var s,p,u,d,h,m,g,v,b,x,f=n,w=r[o+1],y="steps"===e;if(y&&(s=E.xNumSteps[o]),s||(s=w-f),!1!==f)for(void 0===w&&(w=f),s=Math.max(s,1e-7),p=f;p<=w;p=(p+s).toFixed(7)/1){for(v=(h=(d=E.toStepping(p))-c)/t,x=h/(b=Math.round(v)),u=1;u<=b;u+=1)i[(m=c+u*x).toFixed(5)]=["x",0];g=r.indexOf(p)>-1?k:y?U:P,!o&&a&&(g=0),p===w&&l||(i[d.toFixed(5)]=[p,g]),c=d}}),i}(i,n,s),l=t.format||{to:Math.round};return y.appendChild(O(a,o,l))}function R(){var t=l.getBoundingClientRect(),e="offset"+["Width","Height"][f.ort];return 0===f.ort?t.width||l[e]:t.height||l[e]}function T(n,i,o,s){var e=function(t){return!!(t
