{% extends 'base.html' %}
{% load static %}

{% block title %}Schimbare Parolă - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <img src="{% static 'images/user-placeholder.png' %}" alt="User" class="rounded-circle" width="50">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ user.get_full_name }}</h6>
                            <small class="text-muted">{{ user.email }}</small>
                        </div>
                    </div>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'main:profile' %}">
                                <i class="fas fa-user me-2"></i>Profil
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'main:orders' %}">
                                <i class="fas fa-shopping-bag me-2"></i>Comenzile mele
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="{% url 'main:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Deconectare
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Change Password Form -->
        <div class="col-lg-9">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="card-title mb-4">Schimbare parolă</h3>

                    {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <label for="old_password" class="form-label">Parola actuală</label>
                            <input type="password" class="form-control" id="old_password" name="old_password" required>
                            <div class="invalid-feedback">
                                Te rugăm să introduci parola actuală.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="new_password1" class="form-label">Parola nouă</label>
                            <input type="password" class="form-control" id="new_password1" name="new_password1" 
                                   minlength="8" required>
                            <div class="invalid-feedback">
                                Parola trebuie să aibă minim 8 caractere.
                            </div>
                            <div class="form-text">
                                <ul class="mb-0">
                                    <li>Parola trebuie să aibă minim 8 caractere</li>
                                    <li>Parola nu poate fi similară cu numele sau emailul tău</li>
                                    <li>Parola nu poate fi una comună (ex: "password123")</li>
                                    <li>Parola trebuie să conțină atât litere cât și cifre</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="new_password2" class="form-label">Confirmă parola nouă</label>
                            <input type="password" class="form-control" id="new_password2" name="new_password2" 
                                   minlength="8" required>
                            <div class="invalid-feedback">
                                Te rugăm să confirmi parola nouă.
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key me-2"></i>Schimbă parola
                            </button>
                            <a href="{% url 'main:profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Anulează
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Form validation
    (function () {
        'use strict'
        var forms = document.querySelectorAll('.needs-validation')
        Array.prototype.slice.call(forms).forEach(function (form) {
            form.addEventListener('submit', function (event) {
                if (!form.checkValidity()) {
                    event.preventDefault()
                    event.stopPropagation()
                }

                // Check if passwords match
                const password1 = document.getElementById('new_password1')
                const password2 = document.getElementById('new_password2')
                if (password1.value !== password2.value) {
                    event.preventDefault()
                    password2.setCustomValidity('Parolele nu coincid')
                } else {
                    password2.setCustomValidity('')
                }

                form.classList.add('was-validated')
            }, false)
        })

        // Clear custom validity when typing
        document.getElementById('new_password2').addEventListener('input', function() {
            this.setCustomValidity('')
        })
    })()
</script>
{% endblock %}