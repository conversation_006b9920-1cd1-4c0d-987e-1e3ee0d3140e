{% extends 'base.html' %}
{% load static %}

{% block title %}Editare Profil - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <h1 class="mb-4">Editare Profil</h1>

            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <!-- Avatar Upload -->
                        <div class="mb-4 text-center">
                            <img src="{{ user.profile.get_avatar_url }}" 
                                 alt="{{ user.get_full_name }}" 
                                 class="rounded-circle img-thumbnail mb-3"
                                 style="width: 150px; height: 150px; object-fit: cover;">
                            <div class="mb-3">
                                <label for="avatar" class="form-label">Poză profil</label>
                                <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                                <div class="form-text">Format acceptat: JPG, PNG. Dimensiune maximă: 2MB</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prenume *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ user.first_name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nume *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ user.last_name }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user.email }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Telefon</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ user.profile.phone }}">
                        </div>

                        <div class="mb-3">
                            <label for="county" class="form-label">Județ</label>
                            <select class="form-select" id="county" name="county">
                                <option value="">Selectează județul</option>
                                {% for county in counties %}
                                <option value="{{ county.id }}" 
                                        {% if user.profile.county.id == county.id %}selected{% endif %}>
                                    {{ county.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="city" class="form-label">Oraș</label>
                            <input type="text" class="form-control" id="city" name="city" 
                                   value="{{ user.profile.city }}">
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Adresă</label>
                            <input type="text" class="form-control" id="address" name="address" 
                                   value="{{ user.profile.address }}">
                        </div>

                        <div class="mb-3">
                            <label for="postal_code" class="form-label">Cod poștal</label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                   value="{{ user.profile.postal_code }}">
                        </div>

                        <div class="mb-4">
                            <div class="form-check mb-2">
                                <input type="checkbox" class="form-check-input" id="newsletter" name="newsletter" 
                                       {% if user.profile.newsletter %}checked{% endif %}>
                                <label class="form-check-label" for="newsletter">
                                    Vreau să primesc newsletter
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="order_updates" name="order_updates" 
                                       {% if user.profile.order_updates %}checked{% endif %}>
                                <label class="form-check-label" for="order_updates">
                                    Vreau să primesc notificări despre comenzi
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>Salvează modificările
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{% url 'main:profile' %}" class="text-success">
                    <i class="fas fa-arrow-left me-2"></i>Înapoi la profil
                </a>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// Auto-hide alerts after 3 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});

// Preview avatar image
document.getElementById('avatar').addEventListener('change', function(e) {
    if (e.target.files && e.target.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.querySelector('img.rounded-circle').src = e.target.result;
        }
        reader.readAsDataURL(e.target.files[0]);
    }
});
</script>
{% endblock %}

{% endblock %}