{% extends 'base.html' %}
{% load static %}

{% block title %}Comenzile mele - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <img src="{% static 'images/user-placeholder.png' %}" alt="User" class="rounded-circle" width="50">
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ user.get_full_name }}</h6>
                            <small class="text-muted">{{ user.email }}</small>
                        </div>
                    </div>

                    <hr>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'main:profile' %}">
                                <i class="fas fa-user me-2"></i>Profil
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'main:orders' %}">
                                <i class="fas fa-shopping-bag me-2"></i>Comenzile mele
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-danger" href="{% url 'main:logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i>Deconectare
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Orders List -->
        <div class="col-lg-9">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h3 class="card-title mb-4">Comenzile mele</h3>

                    {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                    {% endif %}

                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Comandă</th>
                                    <th>Data</th>
                                    <th>Status</th>
                                    <th>Total</th>
                                    <th>Acțiuni</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>#{{ order.id }}</td>
                                    <td>{{ order.created_at|date:"d.m.Y H:i" }}</td>
                                    <td>
                                        <span class="badge bg-{{ order.get_status_color }}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ order.total_amount }} Lei</td>
                                    <td>
                                        <a href="{% url 'main:order_detail' order.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if order.status == 'pending' %}
                                        <a href="{% url 'main:order_cancel' order.id %}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-times"></i>
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <h5>Nu ai nicio comandă încă</h5>
                        <p class="text-muted">Comenzile tale vor apărea aici după ce plasezi prima comandă.</p>
                        <a href="{% url 'main:shop' %}" class="btn btn-outline-success">
                            <i class="fas fa-shopping-cart me-2"></i>Începe cumpărăturile
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}