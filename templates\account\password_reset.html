{% extends 'base.html' %}
{% load static %}

{% block title %}Resetare parolă - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <h1 class="mb-4">Resetare parolă</h1>

            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-body">
                    <p class="card-text mb-4">
                        Introduceți adresa de email asociată contului dumneavoastră și vă vom trimite un link pentru resetarea parolei.
                    </p>
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <div class="mb-4">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                Introduceți o adresă de email validă.
                            </div>
                        </div>
                        <button type="submit" class="btn btn-success w-100" id="submitBtn">
                            <i class="fas fa-paper-plane me-2"></i>Trimite link de resetare
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <p>
                    <a href="{% url 'main:login' %}" class="text-success">
                        <i class="fas fa-arrow-left me-2"></i>Înapoi la autentificare
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');
    const spinner = submitBtn.querySelector('.spinner-border');

    // Form submission
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            submitBtn.disabled = true;
            spinner.classList.remove('d-none');
        }
        form.classList.add('was-validated');
    });

    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});
</script>
{% endblock %}
