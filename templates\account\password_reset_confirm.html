{% extends 'base.html' %}
{% load static %}

{% block title %}Setare parolă nouă - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <h1 class="mb-4">Setare parolă nouă</h1>

            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            {% if validlink %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="password" class="form-label">Parolă nouă *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required
                                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Parola trebuie să conțină:
                                <ul class="mb-0">
                                    <li id="length" class="text-danger">Minim 8 caractere</li>
                                    <li id="uppercase" class="text-danger">O literă mare</li>
                                    <li id="lowercase" class="text-danger">O literă mică</li>
                                    <li id="number" class="text-danger">Un număr</li>
                                    <li id="special" class="text-danger">Un caracter special (@$!%*?&)</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="confirm_password" class="form-label">Confirmă parola nouă *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Parolele nu coincid.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100" id="submitBtn" disabled>
                            <i class="fas fa-key me-2"></i>Setează parola nouă
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </form>
                </div>
            </div>
            {% else %}
            <div class="alert alert-danger" role="alert">
                Link-ul de resetare a parolei este invalid sau a expirat. 
                Vă rugăm să solicitați un nou link de resetare.
            </div>
            <div class="text-center mt-4">
                <a href="{% url 'main:password_reset' %}" class="btn btn-success">
                    <i class="fas fa-redo me-2"></i>Solicită un nou link
                </a>
            </div>
            {% endif %}

            <div class="text-center mt-4">
                <p>
                    <a href="{% url 'main:login' %}" class="text-success">
                        <i class="fas fa-arrow-left me-2"></i>Înapoi la autentificare
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submitBtn');
    
    // Password validation criteria
    const criteria = {
        length: str => str.length >= 8,
        uppercase: str => /[A-Z]/.test(str),
        lowercase: str => /[a-z]/.test(str),
        number: str => /\d/.test(str),
        special: str => /[@$!%*?&]/.test(str)
    };

    // Password validation
    function validatePassword() {
        const pwd = password.value;
        let valid = true;

        // Check each criterion
        Object.keys(criteria).forEach(criterion => {
            const element = document.getElementById(criterion);
            if (criteria[criterion](pwd)) {
                element.classList.remove('text-danger');
                element.classList.add('text-success');
            } else {
                element.classList.remove('text-success');
                element.classList.add('text-danger');
                valid = false;
            }
        });

        return valid;
    }

    // Check if form is valid
    function checkFormValidity() {
        const isPasswordValid = validatePassword();
        const isConfirmValid = password.value === confirmPassword.value;
        submitBtn.disabled = !(isPasswordValid && isConfirmValid);
    }

    // Password visibility toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const type = confirmPassword.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPassword.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    // Event listeners
    password.addEventListener('input', checkFormValidity);
    confirmPassword.addEventListener('input', checkFormValidity);

    // Form submission
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        } else {
            const submitBtn = this.querySelector('button[type="submit"]');
            const spinner = submitBtn.querySelector('.spinner-border');
            submitBtn.disabled = true;
            spinner.classList.remove('d-none');
        }
        form.classList.add('was-validated');
    });

    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});
</script>
{% endblock %}
