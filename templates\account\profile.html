{% extends 'base.html' %}
{% load static %}

{% block title %}Profil - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row">
        <!-- Profile Sidebar -->
        <div class="col-lg-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img src="{{ user.profile.get_avatar_url }}" 
                             alt="{{ user.get_full_name }}" 
                             class="rounded-circle img-thumbnail"
                             style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <h5 class="card-title mb-0">{{ user.get_full_name }}</h5>
                    <p class="text-muted small">{{ user.email }}</p>
                    <a href="{% url 'main:edit_profile' %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-edit me-2"></i><PERSON><PERSON><PERSON><PERSON> profil
                    </a>
                </div>
            </div>

            <div class="list-group shadow-sm mb-4">
                <a href="{% url 'main:profile' %}" class="list-group-item list-group-item-action active">
                    <i class="fas fa-user me-2"></i>Profil
                </a>
                <a href="{% url 'main:orders' %}" class="list-group-item list-group-item-action">
                    <i class="fas fa-shopping-bag me-2"></i>Comenzi
                </a>
                <a href="{% url 'main:change_password' %}" class="list-group-item list-group-item-action">
                    <i class="fas fa-key me-2"></i>Schimbă parola
                </a>
            </div>
        </div>

        <!-- Profile Content -->
        <div class="col-lg-9">
            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informații profil</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Nume:</strong> {{ user.get_full_name }}</p>
                            <p><strong>Email:</strong> {{ user.email }}</p>
                            <p><strong>Telefon:</strong> {{ user.profile.phone|default:"Nespecificat" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Adresă:</strong> {{ user.profile.address|default:"Nespecificat" }}</p>
                            <p><strong>Oraș:</strong> {{ user.profile.city|default:"Nespecificat" }}</p>
                            <p><strong>Județ:</strong> {{ user.profile.county.name|default:"Nespecificat" }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Preferințe</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p>
                                <i class="fas fa-envelope me-2"></i>
                                Newsletter: 
                                {% if user.profile.newsletter %}
                                <span class="badge bg-success">Activat</span>
                                {% else %}
                                <span class="badge bg-secondary">Dezactivat</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <i class="fas fa-bell me-2"></i>
                                Notificări comenzi: 
                                {% if user.profile.order_updates %}
                                <span class="badge bg-success">Activate</span>
                                {% else %}
                                <span class="badge bg-secondary">Dezactivate</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// Auto-hide alerts after 3 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});
</script>
{% endblock %}

{% endblock %}