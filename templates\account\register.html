{% extends 'base.html' %}
{% load static %}

{% block title %}Înregistrare - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <h1 class="mb-4">Înregistrare</h1>

            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post" class="register-form">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prenume *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ form_data.first_name|default:'' }}" required
                                       pattern="[A-Za-zĂăÂâÎîȘșȚț\s-]+" minlength="2">
                                <div class="invalid-feedback">
                                    Introduceți un prenume valid (minim 2 caractere).
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nume *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ form_data.last_name|default:'' }}" required
                                       pattern="[A-Za-zĂăÂâÎîȘșȚț\s-]+" minlength="2">
                                <div class="invalid-feedback">
                                    Introduceți un nume valid (minim 2 caractere).
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ form_data.email|default:'' }}" required>
                            <div class="invalid-feedback">
                                Introduceți o adresă de email validă.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Telefon</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ form_data.phone|default:'' }}"
                                   pattern="^(07[0-9]{8}|02[0-9]{8}|03[0-9]{8})$">
                            <div class="form-text">Format: 07xxxxxxxx sau 02xxxxxxxx sau 03xxxxxxxx</div>
                            <div class="invalid-feedback">
                                Introduceți un număr de telefon valid.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="county" class="form-label">Județ</label>
                            <select class="form-select" id="county" name="county">
                                <option value="">Selectați județul</option>
                                {% for county in counties %}
                                <option value="{{ county.id }}" {% if form_data.county == county.id %}selected{% endif %}>
                                    {{ county.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Parolă *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" required
                                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                Parola trebuie să conțină:
                                <ul class="mb-0">
                                    <li id="length" class="text-danger">Minim 8 caractere</li>
                                    <li id="uppercase" class="text-danger">O literă mare</li>
                                    <li id="lowercase" class="text-danger">O literă mică</li>
                                    <li id="number" class="text-danger">Un număr</li>
                                    <li id="special" class="text-danger">Un caracter special (@$!%*?&)</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmă parola *</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Parolele nu coincid.
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                                <label class="form-check-label" for="terms">
                                    Am citit și sunt de acord cu <a href="{% url 'main:terms' %}" target="_blank">Termenii și condițiile</a> *
                                </label>
                                <div class="invalid-feedback">
                                    Trebuie să acceptați termenii și condițiile.
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="g-recaptcha" data-sitekey="{{ recaptcha_site_key }}"></div>
                            <div class="invalid-feedback">
                                Vă rugăm să confirmați că nu sunteți robot.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100" id="submitBtn" disabled>
                            <i class="fas fa-user-plus me-2"></i>Creează cont
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                        </button>
                    </form>
                </div>
            </div>

            <div class="text-center mt-4">
                <p>Ai deja cont? <a href="{% url 'main:login' %}" class="text-success">Autentifică-te</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- reCAPTCHA -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    const submitBtn = document.getElementById('submitBtn');
    const terms = document.getElementById('terms');
    const requiredFields = form.querySelectorAll('[required]');

    // Password validation criteria
    const criteria = {
        length: str => str.length >= 8,
        uppercase: str => /[A-Z]/.test(str),
        lowercase: str => /[a-z]/.test(str),
        number: str => /\d/.test(str),
        special: str => /[@$!%*?&]/.test(str)
    };

    // Password validation
    function validatePassword() {
        const pwd = password.value;
        let valid = true;

        Object.keys(criteria).forEach(criterion => {
            const element = document.getElementById(criterion);
            if (criteria[criterion](pwd)) {
                element.classList.remove('text-danger');
                element.classList.add('text-success');
            } else {
                element.classList.remove('text-success');
                element.classList.add('text-danger');
                valid = false;
            }
        });

        return valid;
    }

    // Check if form is valid
    function checkFormValidity() {
        const isPasswordValid = validatePassword();
        const isConfirmValid = password.value === confirmPassword.value;
        const isTermsAccepted = terms.checked;
        const areRequiredFieldsValid = Array.from(requiredFields).every(field => {
            if (field.type === 'checkbox') {
                return field.checked;
            }
            return field.value.trim() !== '';
        });

        if (isPasswordValid && isConfirmValid && isTermsAccepted && areRequiredFieldsValid) {
            submitBtn.disabled = false;
        }
    }

    // Password visibility toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const type = confirmPassword.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPassword.setAttribute('type', type);
        this.querySelector('i').classList.toggle('fa-eye');
        this.querySelector('i').classList.toggle('fa-eye-slash');
    });

    // Event listeners for all required fields
    requiredFields.forEach(field => {
        field.addEventListener('input', checkFormValidity);
    });

    // Event listeners for password and terms
    password.addEventListener('input', checkFormValidity);
    confirmPassword.addEventListener('input', checkFormValidity);
    terms.addEventListener('change', checkFormValidity);

    // Form submission
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        const requiredFieldsValid = Array.from(requiredFields).every(field => {
            if (field.type === 'checkbox') {
                if (!field.checked) {
                    field.classList.add('is-invalid');
                    return false;
                }
            } else if (field.value.trim() === '') {
                field.classList.add('is-invalid');
                return false;
            }
            field.classList.remove('is-invalid');
            return true;
        });

        if (!requiredFieldsValid) {
            return;
        }

        if (!grecaptcha.getResponse()) {
            alert('Vă rugăm să confirmați că nu sunteți robot.');
            return;
        }

        const submitBtn = this.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');
        
        this.submit();
    });

    // Auto-hide alerts
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});
</script>
{% endblock %}
