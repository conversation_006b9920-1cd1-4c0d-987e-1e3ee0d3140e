{% extends 'base.html' %}
{% load static %}

{% block title %}Adaugă Magazin Online - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.form-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0099ff 100%);
    padding: 60px 0;
    color: white;
    margin-bottom: 40px;
}

.form-section {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-section h3 {
    color: #0d6efd;
    margin-bottom: 20px;
}

.image-preview {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.preview-item {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 10px;
    overflow: hidden;
}

.preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-item .remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.eco-criteria {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.eco-checkbox {
    display: none;
}

.eco-label {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.eco-label:hover {
    border-color: #0d6efd;
}

.eco-checkbox:checked + .eco-label {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.eco-label i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: #28a745;
}

.drag-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.drag-drop-zone:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.drag-drop-zone i {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.image-requirements {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 10px;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
}

.url-preview {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.url-preview a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: inherit;
}

.url-preview i {
    margin-right: 10px;
    color: #0d6efd;
}
</style>
{% endblock %}

{% block content %}
<div class="form-header text-center">
    <div class="container">
        <h1 class="display-4">Adaugă Magazin Online</h1>
        <p class="lead">Completează informațiile despre magazinul tău</p>
    </div>
</div>

<div class="container mb-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <form method="post" enctype="multipart/form-data" id="addStoreForm">
                {% csrf_token %}
                
                <!-- Informații de bază -->
                <div class="form-section">
                    <h3><i class="fas fa-info-circle me-2"></i>Informații de Bază</h3>
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Nume Magazin *</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {{ form.name.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.county.id_for_label }}" class="form-label">Județ Sediu *</label>
                        {{ form.county }}
                        {% if form.county.errors %}
                            <div class="invalid-feedback">
                                {{ form.county.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Adresă Sediu *</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {{ form.address.errors }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.website_url.id_for_label }}" class="form-label">Website *</label>
                        {{ form.website_url }}
                        {% if form.website_url.errors %}
                            <div class="invalid-feedback">
                                {{ form.website_url.errors }}
                            </div>
                        {% endif %}
                        <div class="url-preview" id="urlPreview" style="display: none;">
                            <a href="#" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                <span id="previewUrl"></span>
                            </a>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Descriere *</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {{ form.description.errors }}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Descrie magazinul, produsele și serviciile oferite. Maxim 400 de cuvinte.
                        </div>
                    </div>
                </div>
                
                <!-- Imagini -->
                <div class="form-section">
                    <h3><i class="fas fa-images me-2"></i>Imagini</h3>
                    
                    {{ formset.management_form }}
                    
                    <div class="drag-drop-zone" id="dropZone">
                        <i class="fas fa-cloud-upload-alt d-block"></i>
                        <p class="mb-2">Trage și plasează imagini aici sau click pentru a selecta</p>
                        <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
                    </div>
                    
                    <div class="image-requirements">
                        <p class="mb-1">Cerințe imagini:</p>
                        <ul class="mb-0">
                            <li>Maxim 3 imagini</li>
                            <li>Format: JPG, PNG</li>
                            <li>Dimensiune maximă: 5MB per imagine</li>
                            <li>Rezoluție minimă: 800x600 pixeli</li>
                        </ul>
                    </div>
                    
                    <div class="image-preview" id="imagePreview"></div>
                    
                    {% if formset.non_form_errors %}
                        <div class="invalid-feedback">
                            {{ formset.non_form_errors }}
                        </div>
                    {% endif %}
                </div>
                
                <!-- Criterii Ecologice -->
                <div class="form-section">
                    <h3><i class="fas fa-leaf me-2"></i>Criterii Ecologice</h3>
                    <p class="text-muted mb-4">
                        Selectează criteriile pe care le îndeplinești. Listările care îndeplinesc 
                        toate criteriile vor primi badge-ul "Eco-friendly".
                    </p>
                    
                    <div class="eco-criteria">
                        <div>
                            {{ form.eco_packaging }}
                            <label for="{{ form.eco_packaging.id_for_label }}" class="eco-label">
                                <i class="fas fa-box"></i>
                                <span>
                                    <strong>Ambalaje Eco</strong><br>
                                    <small class="text-muted">Folosești ambalaje biodegradabile</small>
                                </span>
                            </label>
                        </div>
                        
                        <div>
                            {{ form.sustainable_sourcing }}
                            <label for="{{ form.sustainable_sourcing.id_for_label }}" class="eco-label">
                                <i class="fas fa-leaf"></i>
                                <span>
                                    <strong>Aprovizionare Sustenabilă</strong><br>
                                    <small class="text-muted">Produse de la furnizori responsabili</small>
                                </span>
                            </label>
                        </div>
                        
                        <div>
                            {{ form.carbon_neutral }}
                            <label for="{{ form.carbon_neutral.id_for_label }}" class="eco-label">
                                <i class="fas fa-truck"></i>
                                <span>
                                    <strong>Livrare Carbon Neutră</strong><br>
                                    <small class="text-muted">Compensezi amprenta de carbon</small>
                                </span>
                            </label>
                        </div>
                        
                        <div>
                            {{ form.ethical_labor }}
                            <label for="{{ form.ethical_labor.id_for_label }}" class="eco-label">
                                <i class="fas fa-hands-helping"></i>
                                <span>
                                    <strong>Practici Etice</strong><br>
                                    <small class="text-muted">Condiții de muncă echitabile</small>
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-check me-2"></i>Trimite Listarea
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handler pentru previzualizare URL
    var urlInput = document.getElementById('{{ form.website_url.id_for_label }}');
    var urlPreview = document.getElementById('urlPreview');
    var previewUrl = document.getElementById('previewUrl');
    
    urlInput.addEventListener('input', function() {
        var url = this.value.trim();
        if (url) {
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }
            urlPreview.style.display = 'block';
            previewUrl.textContent = url;
            urlPreview.querySelector('a').href = url;
        } else {
            urlPreview.style.display = 'none';
        }
    });
    
    // Handler pentru drag & drop imagini
    var dropZone = document.getElementById('dropZone');
    var imageInput = document.getElementById('imageInput');
    var imagePreview = document.getElementById('imagePreview');
    
    dropZone.addEventListener('click', function() {
        imageInput.click();
    });
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.style.borderColor = '#0d6efd';
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.style.borderColor = '#dee2e6';
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.style.borderColor = '#dee2e6';
        
        var files = e.dataTransfer.files;
        handleFiles(files);
    });
    
    imageInput.addEventListener('change', function() {
        handleFiles(this.files);
    });
    
    function handleFiles(files) {
        if (files.length > 3) {
            alert('Poți încărca maxim 3 imagini.');
            return;
        }
        
        imagePreview.innerHTML = '';
        
        Array.from(files).forEach(function(file, index) {
            if (!file.type.startsWith('image/')) {
                alert('Te rog încarcă doar imagini.');
                return;
            }
            
            var reader = new FileReader();
            reader.onload = function(e) {
                var div = document.createElement('div');
                div.className = 'preview-item';
                div.innerHTML = `
                    <img src="${e.target.result}" alt="Preview">
                    <button type="button" class="remove-image" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                imagePreview.appendChild(div);
            };
            reader.readAsDataURL(file);
        });
        
        // Actualizează formset-ul
        updateFormset(files);
    }
    
    function updateFormset(files) {
        // Curăță formset-ul existent
        var totalForms = document.getElementById('id_form-TOTAL_FORMS');
        var initialForms = document.getElementById('id_form-INITIAL_FORMS');
        
        totalForms.value = files.length;
        initialForms.value = '0';
        
        // Adaugă fișierele în formset
        Array.from(files).forEach(function(file, index) {
            var fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.name = `form-${index}-image`;
            fileInput.id = `id_form-${index}-image`;
            fileInput.style.display = 'none';
            
            var dt = new DataTransfer();
            dt.items.add(file);
            fileInput.files = dt.files;
            
            document.getElementById('addStoreForm').appendChild(fileInput);
        });
    }
    
    // Handler pentru ștergere imagini
    imagePreview.addEventListener('click', function(e) {
        if (e.target.closest('.remove-image')) {
            var index = e.target.closest('.remove-image').dataset.index;
            var dt = new DataTransfer();
            var files = imageInput.files;
            
            for (var i = 0; i < files.length; i++) {
                if (i != index) dt.items.add(files[i]);
            }
            
            imageInput.files = dt.files;
            handleFiles(imageInput.files);
        }
    });
});
</script>
{% endblock %}
