{% extends 'base.html' %}
{% load static %}

{% block title %}{{ listing.name }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
.listing-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0099ff 100%);
    padding: 60px 0;
    color: white;
    margin-bottom: 40px;
}

.listing-header .badge {
    font-size: 1rem;
    padding: 8px 15px;
    margin-left: 10px;
}

.listing-section {
    background-color: #fff;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.listing-section h3 {
    color: #0d6efd;
    margin-bottom: 20px;
}

.gallery {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 30px;
}

.gallery-main {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.gallery-thumbs {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.gallery-thumb {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 10px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s;
}

.gallery-thumb:hover,
.gallery-thumb.active {
    opacity: 1;
}

#map {
    height: 400px;
    border-radius: 15px;
}

.eco-criteria {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.eco-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: #f8f9fa;
}

.eco-item.active {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid #28a745;
}

.eco-item i {
    font-size: 1.5rem;
    margin-right: 15px;
}

.eco-item.active i {
    color: #28a745;
}

.eco-item.inactive i {
    color: #6c757d;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-info-item {
    display: flex;
    align-items: center;
}

.contact-info-item i {
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #0d6efd;
}

.website-preview {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 10px;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.3s;
}

.website-preview:hover {
    background-color: #e9ecef;
}

.website-preview i {
    font-size: 1.5rem;
    margin-right: 15px;
    color: #0d6efd;
}
</style>
{% endblock %}

{% block content %}
<div class="listing-header">
    <div class="container">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h1 class="display-4">{{ listing.name }}</h1>
                <p class="lead mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>{{ listing.county.name }}
                    {% if listing.is_eco_friendly %}
                        <span class="badge bg-success">
                            <i class="fas fa-leaf me-1"></i>Eco-friendly
                        </span>
                    {% endif %}
                </p>
            </div>
            {% if user == listing.user %}
                <a href="#" class="btn btn-light">
                    <i class="fas fa-edit me-2"></i>Editează
                </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="container mb-5">
    <div class="row">
        <div class="col-lg-8">
            <!-- Galerie Imagini -->
            <div class="listing-section">
                <div class="gallery">
                    {% if listing.images.all %}
                        <img src="{{ listing.images.first.image.url }}" alt="{{ listing.name }}" class="gallery-main" id="mainImage">
                        {% if listing.images.count > 1 %}
                            <div class="gallery-thumbs">
                                {% for image in listing.images.all %}
                                    <img src="{{ image.image.url }}" 
                                         alt="Thumbnail {{ forloop.counter }}"
                                         class="gallery-thumb {% if forloop.first %}active{% endif %}"
                                         onclick="changeMainImage(this)">
                                {% endfor %}
                            </div>
                        {% endif %}
                    {% else %}
                        {% if listing.listing_type == 'lake' %}
                            <img src="{% static 'images/lake-placeholder.jpg' %}" alt="{{ listing.name }}" class="gallery-main">
                        {% else %}
                            <img src="{% static 'images/store-placeholder.jpg' %}" alt="{{ listing.name }}" class="gallery-main">
                        {% endif %}
                    {% endif %}
                </div>
            </div>

            <!-- Descriere -->
            <div class="listing-section">
                <h3><i class="fas fa-info-circle me-2"></i>Descriere</h3>
                <div class="description">
                    {{ listing.description|linebreaks }}
                </div>
            </div>

            {% if listing.listing_type == 'lake' %}
                <!-- Hartă pentru Baltă -->
                <div class="listing-section">
                    <h3><i class="fas fa-map-marked-alt me-2"></i>Locație</h3>
                    <div id="map"></div>
                </div>
            {% else %}
                <!-- Website pentru Magazin -->
                <div class="listing-section">
                    <h3><i class="fas fa-globe me-2"></i>Website</h3>
                    <a href="{{ listing.onlinestore.website_url }}" target="_blank" class="website-preview">
                        <i class="fas fa-external-link-alt"></i>
                        <div>
                            <strong>Vizitează Magazinul Online</strong><br>
                            <small class="text-muted">{{ listing.onlinestore.website_url }}</small>
                        </div>
                    </a>
                </div>
            {% endif %}
        </div>

        <div class="col-lg-4">
            <!-- Criterii Ecologice -->
            <div class="listing-section">
                <h3><i class="fas fa-leaf me-2"></i>Criterii Ecologice</h3>
                <div class="eco-criteria">
                    {% if listing.listing_type == 'lake' %}
                        <div class="eco-item {% if listing.fishinglake.catch_release %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-fish"></i>
                            <div>
                                <strong>Catch & Release</strong><br>
                                <small class="text-muted">Promovează practica catch & release</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.fishinglake.water_quality %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-tint"></i>
                            <div>
                                <strong>Calitatea Apei</strong><br>
                                <small class="text-muted">Monitorizează și menține calitatea apei</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.fishinglake.eco_waste %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-recycle"></i>
                            <div>
                                <strong>Gestionare Deșeuri</strong><br>
                                <small class="text-muted">Gestionează ecologic deșeurile</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.fishinglake.biodiversity %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-seedling"></i>
                            <div>
                                <strong>Biodiversitate</strong><br>
                                <small class="text-muted">Protejează biodiversitatea locală</small>
                            </div>
                        </div>
                    {% else %}
                        <div class="eco-item {% if listing.onlinestore.eco_packaging %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-box"></i>
                            <div>
                                <strong>Ambalaje Eco</strong><br>
                                <small class="text-muted">Folosește ambalaje biodegradabile</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.onlinestore.sustainable_sourcing %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-leaf"></i>
                            <div>
                                <strong>Aprovizionare Sustenabilă</strong><br>
                                <small class="text-muted">Produse de la furnizori responsabili</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.onlinestore.carbon_neutral %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-truck"></i>
                            <div>
                                <strong>Livrare Carbon Neutră</strong><br>
                                <small class="text-muted">Compensează amprenta de carbon</small>
                            </div>
                        </div>
                        <div class="eco-item {% if listing.onlinestore.ethical_labor %}active{% else %}inactive{% endif %}">
                            <i class="fas fa-hands-helping"></i>
                            <div>
                                <strong>Practici Etice</strong><br>
                                <small class="text-muted">Condiții de muncă echitabile</small>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Informații Contact -->
            <div class="listing-section">
                <h3><i class="fas fa-address-card me-2"></i>Contact</h3>
                <div class="contact-info">
                    <div class="contact-info-item">
                        <i class="fas fa-user"></i>
                        <div>
                            <strong>Proprietar</strong><br>
                            {{ listing.user.get_full_name|default:listing.user.username }}
                        </div>
                    </div>
                    <div class="contact-info-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <strong>Adresă</strong><br>
                            {{ listing.address }}
                        </div>
                    </div>
                    {% if listing.user.profile.phone %}
                        <div class="contact-info-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>Telefon</strong><br>
                                {{ listing.user.profile.phone }}
                            </div>
                        </div>
                    {% endif %}
                    <div class="contact-info-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <strong>Email</strong><br>
                            {{ listing.user.email }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if listing.listing_type == 'lake' %}
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Inițializare hartă
        var map = L.map('map').setView([{{ listing.fishinglake.latitude }}, {{ listing.fishinglake.longitude }}], 13);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // Adaugă marker
        L.marker([{{ listing.fishinglake.latitude }}, {{ listing.fishinglake.longitude }}])
            .addTo(map)
            .bindPopup('{{ listing.name }}')
            .openPopup();
    </script>
{% endif %}

<script>
function changeMainImage(thumb) {
    // Actualizează imaginea principală
    document.getElementById('mainImage').src = thumb.src;
    
    // Actualizează starea activă a thumbnail-urilor
    document.querySelectorAll('.gallery-thumb').forEach(t => {
        t.classList.remove('active');
    });
    thumb.classList.add('active');
}
</script>
{% endblock %}
