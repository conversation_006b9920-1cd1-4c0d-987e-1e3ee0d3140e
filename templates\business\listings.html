{% extends 'base.html' %}
{% load static %}

{% block title %}Listări Business - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.listing-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0099ff 100%);
    padding: 60px 0;
    color: white;
    margin-bottom: 40px;
}

.listing-card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s;
    height: 100%;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.listing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.listing-image {
    height: 200px;
    object-fit: cover;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.eco-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.listing-type {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.listing-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

.listing-footer i {
    margin-right: 5px;
    color: #6c757d;
}

.filters {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.filters .form-label {
    font-weight: 500;
}

.no-results {
    text-align: center;
    padding: 50px 20px;
    background-color: #f8f9fa;
    border-radius: 15px;
    margin: 20px 0;
}

.no-results i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="listing-header text-center">
    <div class="container">
        <h1 class="display-4">Listări Business</h1>
        <p class="lead">Descoperă bălți de pescuit și magazine online</p>
        {% if user.is_authenticated %}
            <div class="mt-4">
                <a href="{% url 'main:add_lake' %}" class="btn btn-light me-3">
                    <i class="fas fa-water me-2"></i>Adaugă Baltă
                </a>
                <a href="{% url 'main:add_store' %}" class="btn btn-light">
                    <i class="fas fa-store me-2"></i>Adaugă Magazin
                </a>
            </div>
        {% endif %}
    </div>
</div>

<div class="container mb-5">
    <!-- Filters -->
    <div class="filters">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="type" class="form-label">Tip Listare</label>
                <select name="type" id="type" class="form-select">
                    <option value="">Toate</option>
                    <option value="lake" {% if request.GET.type == 'lake' %}selected{% endif %}>Bălți de Pescuit</option>
                    <option value="store" {% if request.GET.type == 'store' %}selected{% endif %}>Magazine Online</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="county" class="form-label">Județ</label>
                <select name="county" id="county" class="form-select">
                    <option value="">Toate</option>
                    {% for county in counties %}
                        <option value="{{ county.id }}" {% if request.GET.county == county.id|stringformat:"i" %}selected{% endif %}>
                            {{ county.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="eco" class="form-label">Criterii Eco</label>
                <select name="eco" id="eco" class="form-select">
                    <option value="">Toate</option>
                    <option value="1" {% if request.GET.eco == '1' %}selected{% endif %}>Doar Eco-friendly</option>
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter me-2"></i>Filtrează
                </button>
                {% if request.GET %}
                    <a href="{% url 'main:business_listings' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Resetează
                    </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Listings -->
    {% if lakes or stores %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for lake in lakes %}
                <div class="col">
                    <div class="card listing-card">
                        {% if lake.images.first %}
                            <img src="{{ lake.images.first.image.url }}" class="listing-image" alt="{{ lake.name }}">
                        {% else %}
                            <img src="{% static 'images/lake-placeholder.jpg' %}" class="listing-image" alt="{{ lake.name }}">
                        {% endif %}
                        
                        <span class="listing-type">
                            <i class="fas fa-water me-1"></i>Baltă
                        </span>
                        
                        {% if lake.is_eco_friendly %}
                            <span class="eco-badge">
                                <i class="fas fa-leaf me-1"></i>Eco-friendly
                            </span>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ lake.name }}</h5>
                            <p class="card-text text-muted">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ lake.county.name }}
                            </p>
                            <p class="card-text">{{ lake.description|truncatewords:30 }}</p>
                        </div>
                        
                        <div class="listing-footer">
                            <div>
                                <i class="fas fa-calendar-alt"></i>
                                {{ lake.created_at|date:"d.m.Y" }}
                            </div>
                            <a href="{% url 'main:listing_detail' lake.pk %}" class="btn btn-primary">
                                Vezi Detalii
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
            
            {% for store in stores %}
                <div class="col">
                    <div class="card listing-card">
                        {% if store.images.first %}
                            <img src="{{ store.images.first.image.url }}" class="listing-image" alt="{{ store.name }}">
                        {% else %}
                            <img src="{% static 'images/store-placeholder.jpg' %}" class="listing-image" alt="{{ store.name }}">
                        {% endif %}
                        
                        <span class="listing-type">
                            <i class="fas fa-store me-1"></i>Magazin
                        </span>
                        
                        {% if store.is_eco_friendly %}
                            <span class="eco-badge">
                                <i class="fas fa-leaf me-1"></i>Eco-friendly
                            </span>
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ store.name }}</h5>
                            <p class="card-text text-muted">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ store.county.name }}
                            </p>
                            <p class="card-text">{{ store.description|truncatewords:30 }}</p>
                        </div>
                        
                        <div class="listing-footer">
                            <div>
                                <i class="fas fa-calendar-alt"></i>
                                {{ store.created_at|date:"d.m.Y" }}
                            </div>
                            <a href="{% url 'main:listing_detail' store.pk %}" class="btn btn-primary">
                                Vezi Detalii
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="no-results">
            <i class="fas fa-search mb-3"></i>
            <h3>Nicio listare găsită</h3>
            <p class="text-muted">
                Nu am găsit nicio listare care să corespundă criteriilor tale.
                Încearcă să modifici filtrele sau să revii mai târziu.
            </p>
        </div>
    {% endif %}
</div>
{% endblock %}
