{% extends 'base.html' %}
{% load static %}

{% block title %}List<PERSON><PERSON><PERSON> - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.my-listings-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0099ff 100%);
    padding: 60px 0;
    color: white;
    margin-bottom: 40px;
}

.listing-card {
    border: none;
    border-radius: 15px;
    transition: transform 0.3s;
    height: 100%;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.listing-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.listing-image {
    height: 200px;
    object-fit: cover;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.status-badge.pending {
    background-color: #ffc107;
    color: #000;
}

.status-badge.active {
    background-color: #28a745;
    color: white;
}

.status-badge.rejected {
    background-color: #dc3545;
    color: white;
}

.listing-type {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.listing-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

.listing-footer i {
    margin-right: 5px;
    color: #6c757d;
}

.subscription-alert {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.subscription-alert i {
    font-size: 2rem;
    color: #856404;
    margin-bottom: 15px;
}

.no-listings {
    text-align: center;
    padding: 50px 20px;
    background-color: #f8f9fa;
    border-radius: 15px;
    margin: 20px 0;
}

.no-listings i {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 20px;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-buttons .btn {
    flex: 1;
}
</style>
{% endblock %}

{% block content %}
<div class="my-listings-header text-center">
    <div class="container">
        <h1 class="display-4">Listările Mele</h1>
        <p class="lead">Gestionează-ți listările de afaceri</p>
        
        {% if user.subscription %}
            <div class="mt-4">
                <p class="mb-3">
                    Plan curent: <strong>{{ user.subscription.get_plan_name_display }}</strong>
                    ({{ user.subscription.get_interval_display }})
                </p>
                <div class="btn-group">
                    <a href="{% url 'main:add_lake' %}" class="btn btn-light me-3">
                        <i class="fas fa-water me-2"></i>Adaugă Baltă
                    </a>
                    <a href="{% url 'main:add_store' %}" class="btn btn-light">
                        <i class="fas fa-store me-2"></i>Adaugă Magazin
                    </a>
                </div>
            </div>
        {% else %}
            <div class="mt-4">
                <a href="{% url 'main:subscription_plans' %}" class="btn btn-light">
                    <i class="fas fa-crown me-2"></i>Alege un Plan
                </a>
            </div>
        {% endif %}
    </div>
</div>

<div class="container mb-5">
    {% if not user.subscription %}
        <div class="subscription-alert text-center">
            <i class="fas fa-exclamation-circle d-block"></i>
            <h4>Nu ai un abonament activ</h4>
            <p class="text-muted mb-4">
                Pentru a putea adăuga listări, ai nevoie de un abonament activ.
                Alege planul potrivit pentru tine și începe să-ți promovezi afacerea!
            </p>
            <a href="{% url 'main:subscription_plans' %}" class="btn btn-warning">
                Vezi Planurile Disponibile
            </a>
        </div>
    {% elif user.subscription and listings %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for listing in listings %}
                <div class="col">
                    <div class="card listing-card">
                        {% if listing.images.first %}
                            <img src="{{ listing.images.first.image.url }}" class="listing-image" alt="{{ listing.name }}">
                        {% else %}
                            {% if listing.listing_type == 'lake' %}
                                <img src="{% static 'images/lake-placeholder.jpg' %}" class="listing-image" alt="{{ listing.name }}">
                            {% else %}
                                <img src="{% static 'images/store-placeholder.jpg' %}" class="listing-image" alt="{{ listing.name }}">
                            {% endif %}
                        {% endif %}
                        
                        <span class="listing-type">
                            {% if listing.listing_type == 'lake' %}
                                <i class="fas fa-water me-1"></i>Baltă
                            {% else %}
                                <i class="fas fa-store me-1"></i>Magazin
                            {% endif %}
                        </span>
                        
                        <span class="status-badge {{ listing.status }}">
                            {% if listing.status == 'pending' %}
                                <i class="fas fa-clock me-1"></i>În Așteptare
                            {% elif listing.status == 'active' %}
                                <i class="fas fa-check me-1"></i>Activ
                            {% else %}
                                <i class="fas fa-times me-1"></i>Respins
                            {% endif %}
                        </span>
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ listing.name }}</h5>
                            <p class="card-text text-muted">
                                <i class="fas fa-map-marker-alt me-2"></i>{{ listing.county.name }}
                            </p>
                            <p class="card-text">{{ listing.description|truncatewords:30 }}</p>
                        </div>
                        
                        <div class="listing-footer">
                            <div>
                                <i class="fas fa-calendar-alt"></i>
                                {{ listing.created_at|date:"d.m.Y" }}
                            </div>
                            <div class="action-buttons">
                                <a href="{% url 'main:listing_detail' listing.pk %}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>Vezi
                                </a>
                                <a href="#" class="btn btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i>Editează
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="no-listings">
            <i class="fas fa-clipboard-list mb-3"></i>
            <h3>Nicio listare găsită</h3>
            <p class="text-muted mb-4">
                Nu ai adăugat încă nicio listare.
                Începe prin a adăuga o baltă de pescuit sau un magazin online!
            </p>
            <div class="btn-group">
                <a href="{% url 'main:add_lake' %}" class="btn btn-primary me-3">
                    <i class="fas fa-water me-2"></i>Adaugă Baltă
                </a>
                <a href="{% url 'main:add_store' %}" class="btn btn-primary">
                    <i class="fas fa-store me-2"></i>Adaugă Magazin
                </a>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}
