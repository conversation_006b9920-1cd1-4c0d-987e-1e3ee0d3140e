<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .order-details {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .order-items {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .order-items th,
        .order-items td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .total-row {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #198754;
            color: white !important;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .receipt {
            font-family: 'Courier New', Courier, monospace;
            background: #fff;
            padding: 20px;
            border: 1px solid #ddd;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        .receipt-header {
            text-align: center;
            border-bottom: 1px dashed #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .receipt-footer {
            text-align: center;
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-top: 10px;
        }
        .receipt-item {
            margin: 5px 0;
        }
        .receipt-total {
            border-top: 1px dashed #000;
            margin-top: 10px;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{{ logo_url }}" alt="Răsfățul Pescarului" class="logo">
        <h1>Confirmare Comandă</h1>
    </div>

    <p>Dragă {{ order.full_name }},</p>

    <p>Îți mulțumim pentru comanda plasată la Răsfățul Pescarului. Mai jos găsești detaliile comenzii tale:</p>

    <div class="receipt">
        <div class="receipt-header">
            RĂSFĂȚUL PESCARULUI
            {{ settings.COMPANY_ADDRESS }}
            Tel: {{ settings.COMPANY_PHONE }}
            
            BON FISCAL
            Nr. Comandă: #{{ order.id }}
            Data: {{ order.created_at|date:"d.m.Y H:i" }}
            ----------------------------------------
        </div>

        {% for item in order.items.all %}
        <div class="receipt-item">
            {{ item.product.name }}
            {{ item.quantity }} x {{ item.unit_price }} Lei = {{ item.total_price }} Lei
        </div>
        {% endfor %}

        <div class="receipt-total">
            Subtotal: {{ order.subtotal }} Lei
            {% if order.shipping_cost > 0 %}
            Transport: {{ order.shipping_cost }} Lei
            {% else %}
            Transport: GRATUIT
            {% endif %}
            ----------------------------------------
            TOTAL: {{ order.total_amount }} Lei
            
            Metodă plată: {{ order.get_payment_method_display }}
        </div>

        <div class="receipt-footer">
            Vă mulțumim pentru comandă!
            www.rasfatulpescarului.ro
        </div>
    </div>

    <h3>Adresă livrare:</h3>
    <div class="order-details">
        {{ shipping_address|linebreaks }}
    </div>

    <div style="text-align: center;">
        <a href="{{ order_url }}" class="button">Vezi detalii comandă</a>
    </div>

    <div class="footer">
        <p>Pentru orice întrebări sau nelămuriri, nu ezita să ne contactezi la adresa <a href="mailto:{{ settings.COMPANY_EMAIL }}">{{ settings.COMPANY_EMAIL }}</a> sau la numărul de telefon <a href="tel:{{ settings.COMPANY_PHONE }}">{{ settings.COMPANY_PHONE }}</a>.</p>
        <p>© {{ current_year }} Răsfățul Pescarului. Toate drepturile rezervate.</p>
    </div>
</body>
</html>