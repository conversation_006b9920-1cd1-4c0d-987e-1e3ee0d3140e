{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'main:home' %}">Acasă</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'main:fishing_locations' %}">Bălți de pescuit</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ county.name }}</li>
                </ol>
            </nav>
            <h1 class="mb-3">Bălți de pescuit în {{ county.name }}</h1>
            <p class="lead">Descoperă cele mai bune locuri de pescuit din județul {{ county.name }}</p>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <p class="mb-0">{{ lakes.count }} rezultate găsite</p>
                </div>
                <div>
                    <a href="{% url 'main:locations_map' %}" class="btn btn-outline-success">
                        <i class="fas fa-map-marker-alt me-2"></i>Vezi pe hartă
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
        {% for lake in lakes %}
        <div class="col">
            <div class="card h-100">
                {% if lake.image %}
                <img src="{{ lake.image.url }}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                {% else %}
                <img src="{% static 'images/lake-placeholder.jpg' %}" class="card-img-top" alt="{{ lake.name }}" style="height: 200px; object-fit: cover;">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ lake.name }}</h5>
                    <p class="card-text">{{ lake.description|truncatechars:100 }}</p>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-fish me-2"></i>{{ lake.fish_types }}</li>
                        <li><i class="fas fa-coins me-2"></i>{{ lake.price_per_day }} Lei/zi</li>
                        <li><i class="fas fa-map-marker-alt me-2"></i>{{ lake.address }}</li>
                    </ul>
                    <div class="d-grid">
                        <a href="{% url 'main:lake_detail' lake.id %}" class="btn btn-success">
                            Vezi detalii
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info">
                Nu există bălți de pescuit înregistrate în acest județ.
            </div>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}