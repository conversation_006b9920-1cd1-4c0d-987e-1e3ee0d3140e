{% load static %}
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="{% url 'main:home' %}">
            <img src="{% static 'images/logo.png' %}" alt="Logo" height="30">
            Răsfățul Pescarului
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarMain">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" 
                       href="{% url 'main:home' %}">
                        <i class="fas fa-home"></i> Acasă
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if 'lake' in request.path %}active{% endif %}" 
                       href="{% url 'main:lake_list' %}">
                        <i class="fas fa-water"></i> Bălți
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if 'shop' in request.path %}active{% endif %}" 
                       href="{% url 'main:shop_list' %}">
                        <i class="fas fa-shopping-cart"></i> Magazin
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if 'tutorial' in request.path %}active{% endif %}" 
                       href="{% url 'main:tutorial_list' %}">
                        <i class="fas fa-book"></i> Tutoriale
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if 'solunar' in request.path %}active{% endif %}" 
                       href="{% url 'main:solunar_calendar' %}">
                        <i class="fas fa-moon"></i> Calendar Solunar
                    </a>
                </li>
                
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle {% if 'business' in request.path %}active{% endif %}" 
                       href="#" id="businessDropdown" role="button" 
                       data-bs-toggle="dropdown">
                        <i class="fas fa-briefcase"></i> Business
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="{% url 'main:business_listings' %}">
                                <i class="fas fa-list"></i> Vezi Listări
                            </a>
                        </li>
                        {% if user.is_authenticated %}
                            <li>
                                <a class="dropdown-item" href="{% url 'main:my_listings' %}">
                                    <i class="fas fa-clipboard-list"></i> Listările Mele
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'main:add_lake' %}">
                                    <i class="fas fa-water"></i> Adaugă Baltă
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'main:add_store' %}">
                                    <i class="fas fa-store"></i> Adaugă Magazin
                                </a>
                            </li>
                        {% else %}
                            <li>
                                <a class="dropdown-item" href="{% url 'main:login' %}?next={% url 'main:subscription_plans' %}">
                                    <i class="fas fa-sign-in-alt"></i> Autentificare pentru Listare
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav ms-auto">
                {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link {% if 'cart' in request.path %}active{% endif %}" 
                           href="{% url 'main:cart' %}">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="badge bg-danger">{{ cart_count }}</span>
                        </a>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" 
                           role="button" data-bs-toggle="dropdown">
                            {% if user.profile.avatar %}
                                <img src="{{ user.profile.avatar.url }}" 
                                     alt="Avatar" 
                                     class="rounded-circle me-1"
                                     style="width: 24px; height: 24px; object-fit: cover;">
                            {% else %}
                                <i class="fas fa-user-circle"></i>
                            {% endif %}
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{% url 'main:profile' %}">
                                    <i class="fas fa-user"></i> Profil
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'main:orders' %}">
                                    <i class="fas fa-box"></i> Comenzi
                                </a>
                            </li>
                            {% if user.subscription %}
                                <li>
                                    <a class="dropdown-item" href="{% url 'main:my_listings' %}">
                                        <i class="fas fa-clipboard-list"></i> Listările Mele
                                    </a>
                                </li>
                            {% endif %}
                            {% if user.is_staff %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="{% url 'admin:index' %}">
                                        <i class="fas fa-cog"></i> Administrare
                                    </a>
                                </li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'main:logout' %}">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt"></i> Deconectare
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'main:login' %}">
                            <i class="fas fa-sign-in-alt"></i> Autentificare
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'main:register' %}">
                            <i class="fas fa-user-plus"></i> Înregistrare
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
