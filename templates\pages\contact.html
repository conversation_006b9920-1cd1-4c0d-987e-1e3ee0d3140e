{% extends 'base.html' %}
{% load static %}

{% block title %}Contact - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <div class="row">
        <div class="col-lg-8">
            <h1 class="mb-4">Contact</h1>

            {% if messages %}
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
            {% endif %}

            <div class="card shadow-sm">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="name" class="form-label">Nume complet *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ form_data.name|default:'' }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ form_data.email|default:'' }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subiect *</label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="{{ form_data.subject|default:'' }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">Mesaj *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required>{{ form_data.message|default:'' }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>Trimite mesajul
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Informații de contact</h5>
                    <hr>
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt me-2"></i>{{ settings.COMPANY_ADDRESS }}
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-phone me-2"></i>{{ settings.COMPANY_PHONE }}
                    </p>
                    <p class="mb-2">
                        <i class="fas fa-envelope me-2"></i>{{ settings.COMPANY_EMAIL }}
                    </p>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">Program de lucru</h5>
                    <hr>
                    <p class="mb-2">
                        <strong>Luni - Vineri:</strong><br>
                        09:00 - 18:00
                    </p>
                    <p class="mb-2">
                        <strong>Sâmbătă:</strong><br>
                        10:00 - 14:00
                    </p>
                    <p class="mb-0">
                        <strong>Duminică:</strong><br>
                        Închis
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
// Auto-hide alerts after 3 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 3000);
});
</script>
{% endblock %}

{% endblock %}