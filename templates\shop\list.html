{% extends 'base.html' %}
{% load static %}
{% load main_extras %}
{% load compress %}

{% block title %}Magazin - Răsfățul Pescarului{% endblock %}

{% block external_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.6.3/nouislider.min.css">
{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'main:home' %}">Acasă</a></li>
            <li class="breadcrumb-item active" aria-current="page">Magazin</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3" id="filterSidebar">
            <!-- Filter Form -->
            <form id="filterForm" class="mb-4">
                {% csrf_token %}
                
                <!-- Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Caută</h5>
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="Caută produse..." value="{{ request.GET.search }}">
                            <button class="btn btn-outline-success" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Categorii</h5>
                        <div class="list-group list-group-flush">
                            <label class="list-group-item">
                                <input type="radio" name="category" value="" class="form-check-input me-2"
                                       {% if not request.GET.category %}checked{% endif %}>
                                Toate produsele
                            </label>
                            {% for cat in categories %}
                            <label class="list-group-item">
                                <input type="radio" name="category" value="{{ cat.slug }}" class="form-check-input me-2"
                                       {% if request.GET.category == cat.slug %}checked{% endif %}>
                                {{ cat.name }}
                            </label>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Price Range -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Preț</h5>
                        <div id="priceRange" class="mb-3"></div>
                        <p id="priceRangeLabel" class="text-center mb-0"></p>
                        <input type="hidden" id="minPrice" name="min_price" data-min="0">
                        <input type="hidden" id="maxPrice" name="max_price" data-max="1000">
                    </div>
                </div>

                <!-- Brand Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Brand</h5>
                        <div id="brandFilter">
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Attribute Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Filtre</h5>
                        
                        <!-- Stock Filter -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="inStock" name="in_stock" value="true"
                                       {% if request.GET.in_stock %}checked{% endif %}>
                                <label class="form-check-label" for="inStock">
                                    Doar produse în stoc
                                </label>
                            </div>
                        </div>

                        <!-- Rating Filter -->
                        <div class="mb-3">
                            <label class="form-label">Rating minim</label>
                            <select class="form-select" name="rating">
                                <option value="">Toate</option>
                                {% for i in "54321"|make_list %}
                                <option value="{{ i }}" {% if request.GET.rating == i %}selected{% endif %}>
                                    {{ i }}+ stele
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Dynamic Attribute Filters -->
                        <div id="colorFilter" class="mb-3">
                            <h6>Culoare</h6>
                            <!-- Populated by JavaScript -->
                        </div>

                        <div id="sizeFilter" class="mb-3">
                            <h6>Mărime</h6>
                            <!-- Populated by JavaScript -->
                        </div>

                        <div id="materialFilter" class="mb-3">
                            <h6>Material</h6>
                            <!-- Populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Results Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h4 id="resultsTitle">Toate produsele</h4>
                </div>
                <div class="d-flex align-items-center">
                    <select id="sortSelect" class="form-select me-2" style="width: auto;">
                        <option value="-created_at" selected>Cele mai noi</option>
                        <option value="price" {% if request.GET.ordering == 'price' %}selected{% endif %}>Preț crescător</option>
                        <option value="-price" {% if request.GET.ordering == '-price' %}selected{% endif %}>Preț descrescător</option>
                        <option value="-average_rating" {% if request.GET.ordering == '-average_rating' %}selected{% endif %}>Cele mai bine evaluate</option>
                        <option value="-sales_count" {% if request.GET.ordering == '-sales_count' %}selected{% endif %}>Cele mai vândute</option>
                    </select>
                    <div class="text-muted" id="resultsCount"></div>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="text-center" style="display: none;">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">Se încarcă...</span>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4" id="productsGrid">
                <!-- Populated by JavaScript -->
            </div>

            <!-- Pagination -->
            <div id="pagination" class="mt-4"></div>
        </div>
    </div>
</div>

<!-- Toast Notification -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="cartToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="fas fa-shopping-cart me-2"></i>
            <strong class="me-auto">Coș de cumpărături</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body"></div>
    </div>
</div>

<!-- Mobile Filter Toggle -->
<button id="filterToggle" class="btn btn-success rounded-circle position-fixed d-lg-none"
        style="bottom: 20px; right: 20px; z-index: 1000;">
    <i class="fas fa-filter"></i>
</button>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/14.6.3/nouislider.min.js"></script>
<script src="{% static 'js/shop.js' %}"></script>
{% endblock %}
