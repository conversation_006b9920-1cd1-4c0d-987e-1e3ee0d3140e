{% extends 'base.html' %}
{% load static %}

{% block title %}Tutoriale - Răsfățul Pescarului{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="mb-3">Tutoriale</h1>
            <p class="lead">Învață tehnici de pescuit și sfaturi utile de la experți</p>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3">
            <!-- Categories -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title">Categorii</h5>
                    <div class="list-group list-group-flush">
                        <a href="{% url 'main:tutorials' %}" 
                           class="list-group-item list-group-item-action {% if not category %}active{% endif %}">
                            Toate tutorialele
                        </a>
                        {% for cat in categories %}
                        <a href="{% url 'main:tutorials' %}?category={{ cat.slug }}" 
                           class="list-group-item list-group-item-action {% if category.id == cat.id %}active{% endif %}">
                            {{ cat.name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Featured Videos -->
            {% if featured_videos %}
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">Tutoriale recomandate</h5>
                    {% for video in featured_videos %}
                    <div class="mb-3">
                        <a href="{% url 'main:video_detail' video.id %}" class="text-decoration-none">
                            <div class="ratio ratio-16x9 mb-2">
                                {% if video.thumbnail %}
                                <img src="{{ video.thumbnail.url }}" alt="{{ video.title }}" class="img-fluid rounded">
                                {% else %}
                                <img src="{% static 'images/video-placeholder.png' %}" alt="{{ video.title }}" class="img-fluid rounded">
                                {% endif %}
                            </div>
                            <h6 class="mb-1">{{ video.title }}</h6>
                        </a>
                        <small class="text-muted">{{ video.created_at|date:"d.m.Y" }}</small>
                    </div>
                    {% if not forloop.last %}
                    <hr>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Videos Grid -->
        <div class="col-lg-9">
            {% if videos %}
            <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                {% for video in videos %}
                <div class="col">
                    <div class="card h-100 shadow-sm">
                        <div class="ratio ratio-16x9">
                            {% if video.thumbnail %}
                            <img src="{{ video.thumbnail.url }}" alt="{{ video.title }}" class="card-img-top">
                            {% else %}
                            <img src="{% static 'images/video-placeholder.png' %}" alt="{{ video.title }}" class="card-img-top">
                            {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ video.title }}</h5>
                            <p class="card-text text-muted">{{ video.description|truncatechars:100 }}</p>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">{{ video.created_at|date:"d.m.Y" }}</small>
                                <a href="{% url 'main:video_detail' video.id %}" class="btn btn-sm btn-outline-success">
                                    Vezi tutorialul
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-video fa-3x text-muted mb-3"></i>
                <h3>Nu există tutoriale</h3>
                <p class="text-muted">Nu am găsit niciun tutorial în această categorie.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}